{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 6 2 7\",\n  key: \"1mqr15\"\n}], [\"path\", {\n  d: \"M10 6h4\",\n  key: \"1itunk\"\n}], [\"path\", {\n  d: \"m22 7-2-1\",\n  key: \"1umjhc\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"16\",\n  x: \"4\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"1wxw4b\"\n}], [\"path\", {\n  d: \"M4 11h16\",\n  key: \"mpoxn0\"\n}], [\"path\", {\n  d: \"M8 15h.01\",\n  key: \"a7atzg\"\n}], [\"path\", {\n  d: \"M16 15h.01\",\n  key: \"rnfrdf\"\n}], [\"path\", {\n  d: \"M6 19v2\",\n  key: \"1loha6\"\n}], [\"path\", {\n  d: \"M18 21v-2\",\n  key: \"sqyl04\"\n}]];\nconst BusFront = createLucideIcon(\"bus-front\", __iconNode);\nexport { __iconNode, BusFront as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "BusFront", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\bus-front.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 6 2 7', key: '1mqr15' }],\n  ['path', { d: 'M10 6h4', key: '1itunk' }],\n  ['path', { d: 'm22 7-2-1', key: '1umjhc' }],\n  ['rect', { width: '16', height: '16', x: '4', y: '3', rx: '2', key: '1wxw4b' }],\n  ['path', { d: 'M4 11h16', key: 'mpoxn0' }],\n  ['path', { d: 'M8 15h.01', key: 'a7atzg' }],\n  ['path', { d: 'M16 15h.01', key: 'rnfrdf' }],\n  ['path', { d: 'M6 19v2', key: '1loha6' }],\n  ['path', { d: 'M18 21v-2', key: 'sqyl04' }],\n];\n\n/**\n * @component @name BusFront\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA2IDIgNyIgLz4KICA8cGF0aCBkPSJNMTAgNmg0IiAvPgogIDxwYXRoIGQ9Im0yMiA3LTItMSIgLz4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHg9IjQiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik00IDExaDE2IiAvPgogIDxwYXRoIGQ9Ik04IDE1aC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTVoLjAxIiAvPgogIDxwYXRoIGQ9Ik02IDE5djIiIC8+CiAgPHBhdGggZD0iTTE4IDIxdi0yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bus-front\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BusFront = createLucideIcon('bus-front', __iconNode);\n\nexport default BusFront;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAM,QAAA,GAAWC,gBAAiB,cAAaT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}