{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport { getFirestore, enableNetwork, disableNetwork, connectFirestoreEmulator } from 'firebase/firestore';\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\n\n// Firebase configuration\n// Uses environment variables for different environments\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || \"demo-api-key\",\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || \"demo-project.firebaseapp.com\",\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || \"demo-cyber-pos\",\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || \"demo-project.appspot.com\",\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || \"123456789\",\n  appId: process.env.REACT_APP_FIREBASE_APP_ID || \"demo-app-id\",\n  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID // Optional for Analytics\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Check if we should use emulators\nconst useEmulator = process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';\nconst isDevelopment = process.env.NODE_ENV === 'development';\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\n// Connect to emulators in development (with better error handling)\nif (isDevelopment && useEmulator) {\n  let emulatorsConnected = false;\n  try {\n    // Connect to Auth emulator\n    const authEmulatorHost = process.env.REACT_APP_FIREBASE_AUTH_EMULATOR_HOST || 'localhost:9099';\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`, {\n      disableWarnings: true\n    });\n\n    // Connect to Firestore emulator\n    const firestoreEmulatorHost = process.env.REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST || 'localhost:8080';\n    const [firestoreHost, firestorePort] = firestoreEmulatorHost.split(':');\n    connectFirestoreEmulator(db, firestoreHost, parseInt(firestorePort));\n\n    // Connect to Storage emulator\n    const storageEmulatorHost = process.env.REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';\n    const [storageHost, storagePort] = storageEmulatorHost.split(':');\n    connectStorageEmulator(storage, storageHost, parseInt(storagePort));\n    emulatorsConnected = true;\n    console.log('🔧 Connected to Firebase emulators');\n  } catch (error) {\n    console.log('⚠️ Emulators not available, using production Firebase:', error);\n    console.log('💡 To use emulators, run: firebase emulators:start');\n  }\n\n  // Log connection status\n  if (emulatorsConnected) {\n    console.log('🔥 Using Firebase Emulators for development');\n  } else {\n    console.log('🌐 Using Production Firebase');\n  }\n}\n\n// Enable offline persistence\ntry {\n  enableNetwork(db);\n} catch (error) {\n  console.warn('Failed to enable network for Firestore:', error);\n}\n\n// Offline persistence is now automatically enabled through FirestoreSettings.localCache\n// No need for a separate enableOfflineSupport function with the new approach\nexport const enableOfflineSupport = async () => {\n  // This function is kept for backward compatibility but is no longer needed\n  // Persistence is automatically enabled through the localCache configuration above\n  console.log('Firebase offline persistence is automatically enabled with the new configuration');\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "connectAuthEmulator", "getFirestore", "enableNetwork", "disableNetwork", "connectFirestoreEmulator", "getStorage", "connectStorageEmulator", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_FIREBASE_API_KEY", "authDomain", "REACT_APP_FIREBASE_AUTH_DOMAIN", "projectId", "REACT_APP_FIREBASE_PROJECT_ID", "storageBucket", "REACT_APP_FIREBASE_STORAGE_BUCKET", "messagingSenderId", "REACT_APP_FIREBASE_MESSAGING_SENDER_ID", "appId", "REACT_APP_FIREBASE_APP_ID", "measurementId", "REACT_APP_FIREBASE_MEASUREMENT_ID", "app", "useEmulator", "REACT_APP_USE_FIREBASE_EMULATOR", "isDevelopment", "NODE_ENV", "auth", "db", "storage", "emulatorsConnected", "authEmulatorHost", "REACT_APP_FIREBASE_AUTH_EMULATOR_HOST", "disableWarnings", "firestoreEmulatorHost", "REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST", "firestoreHost", "firestorePort", "split", "parseInt", "storageEmulatorHost", "REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST", "storageHost", "storagePort", "console", "log", "error", "warn", "enableOfflineSupport", "goOffline", "goOnline"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport {\n  getFirestore,\n  enableNetwork,\n  disableNetwork,\n  connectFirestoreEmulator,\n  persistentLocalCache\n} from 'firebase/firestore';\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\n\n// Firebase configuration\n// Uses environment variables for different environments\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || \"demo-api-key\",\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || \"demo-project.firebaseapp.com\",\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || \"demo-cyber-pos\",\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || \"demo-project.appspot.com\",\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || \"123456789\",\n  appId: process.env.REACT_APP_FIREBASE_APP_ID || \"demo-app-id\",\n  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID // Optional for Analytics\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Check if we should use emulators\nconst useEmulator = process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';\nconst isDevelopment = process.env.NODE_ENV === 'development';\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\n// Connect to emulators in development (with better error handling)\nif (isDevelopment && useEmulator) {\n  let emulatorsConnected = false;\n\n  try {\n    // Connect to Auth emulator\n    const authEmulatorHost = process.env.REACT_APP_FIREBASE_AUTH_EMULATOR_HOST || 'localhost:9099';\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`, { disableWarnings: true });\n\n    // Connect to Firestore emulator\n    const firestoreEmulatorHost = process.env.REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST || 'localhost:8080';\n    const [firestoreHost, firestorePort] = firestoreEmulatorHost.split(':');\n    connectFirestoreEmulator(db, firestoreHost, parseInt(firestorePort));\n\n    // Connect to Storage emulator\n    const storageEmulatorHost = process.env.REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';\n    const [storageHost, storagePort] = storageEmulatorHost.split(':');\n    connectStorageEmulator(storage, storageHost, parseInt(storagePort));\n\n    emulatorsConnected = true;\n    console.log('🔧 Connected to Firebase emulators');\n  } catch (error) {\n    console.log('⚠️ Emulators not available, using production Firebase:', error);\n    console.log('💡 To use emulators, run: firebase emulators:start');\n  }\n\n  // Log connection status\n  if (emulatorsConnected) {\n    console.log('🔥 Using Firebase Emulators for development');\n  } else {\n    console.log('🌐 Using Production Firebase');\n  }\n}\n\n// Enable offline persistence\ntry {\n  enableNetwork(db);\n} catch (error) {\n  console.warn('Failed to enable network for Firestore:', error);\n}\n\n// Offline persistence is now automatically enabled through FirestoreSettings.localCache\n// No need for a separate enableOfflineSupport function with the new approach\nexport const enableOfflineSupport = async () => {\n  // This function is kept for backward compatibility but is no longer needed\n  // Persistence is automatically enabled through the localCache configuration above\n  console.log('Firebase offline persistence is automatically enabled with the new configuration');\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\n\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,eAAe;AAC5D,SACEC,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,wBAAwB,QAEnB,oBAAoB;AAC3B,SAASC,UAAU,EAAEC,sBAAsB,QAAQ,kBAAkB;;AAErE;AACA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAI,cAAc;EAChEC,UAAU,EAAEH,OAAO,CAACC,GAAG,CAACG,8BAA8B,IAAI,8BAA8B;EACxFC,SAAS,EAAEL,OAAO,CAACC,GAAG,CAACK,6BAA6B,IAAI,gBAAgB;EACxEC,aAAa,EAAEP,OAAO,CAACC,GAAG,CAACO,iCAAiC,IAAI,0BAA0B;EAC1FC,iBAAiB,EAAET,OAAO,CAACC,GAAG,CAACS,sCAAsC,IAAI,WAAW;EACpFC,KAAK,EAAEX,OAAO,CAACC,GAAG,CAACW,yBAAyB,IAAI,aAAa;EAC7DC,aAAa,EAAEb,OAAO,CAACC,GAAG,CAACa,iCAAiC,CAAC;AAC/D,CAAC;;AAED;AACA,MAAMC,GAAG,GAAG1B,aAAa,CAACS,cAAc,CAAC;;AAEzC;AACA,MAAMkB,WAAW,GAAGhB,OAAO,CAACC,GAAG,CAACgB,+BAA+B,KAAK,MAAM;AAC1E,MAAMC,aAAa,GAAGlB,OAAO,CAACC,GAAG,CAACkB,QAAQ,KAAK,aAAa;;AAE5D;AACA,OAAO,MAAMC,IAAI,GAAG9B,OAAO,CAACyB,GAAG,CAAC;AAChC,OAAO,MAAMM,EAAE,GAAG7B,YAAY,CAACuB,GAAG,CAAC;AACnC,OAAO,MAAMO,OAAO,GAAG1B,UAAU,CAACmB,GAAG,CAAC;;AAEtC;AACA,IAAIG,aAAa,IAAIF,WAAW,EAAE;EAChC,IAAIO,kBAAkB,GAAG,KAAK;EAE9B,IAAI;IACF;IACA,MAAMC,gBAAgB,GAAGxB,OAAO,CAACC,GAAG,CAACwB,qCAAqC,IAAI,gBAAgB;IAC9FlC,mBAAmB,CAAC6B,IAAI,EAAE,UAAUI,gBAAgB,EAAE,EAAE;MAAEE,eAAe,EAAE;IAAK,CAAC,CAAC;;IAElF;IACA,MAAMC,qBAAqB,GAAG3B,OAAO,CAACC,GAAG,CAAC2B,0CAA0C,IAAI,gBAAgB;IACxG,MAAM,CAACC,aAAa,EAAEC,aAAa,CAAC,GAAGH,qBAAqB,CAACI,KAAK,CAAC,GAAG,CAAC;IACvEpC,wBAAwB,CAAC0B,EAAE,EAAEQ,aAAa,EAAEG,QAAQ,CAACF,aAAa,CAAC,CAAC;;IAEpE;IACA,MAAMG,mBAAmB,GAAGjC,OAAO,CAACC,GAAG,CAACiC,wCAAwC,IAAI,gBAAgB;IACpG,MAAM,CAACC,WAAW,EAAEC,WAAW,CAAC,GAAGH,mBAAmB,CAACF,KAAK,CAAC,GAAG,CAAC;IACjElC,sBAAsB,CAACyB,OAAO,EAAEa,WAAW,EAAEH,QAAQ,CAACI,WAAW,CAAC,CAAC;IAEnEb,kBAAkB,GAAG,IAAI;IACzBc,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEC,KAAK,CAAC;IAC5EF,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;;EAEA;EACA,IAAIf,kBAAkB,EAAE;IACtBc,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAC5D,CAAC,MAAM;IACLD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC7C;AACF;;AAEA;AACA,IAAI;EACF7C,aAAa,CAAC4B,EAAE,CAAC;AACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;EACdF,OAAO,CAACG,IAAI,CAAC,yCAAyC,EAAED,KAAK,CAAC;AAChE;;AAEA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C;EACA;EACAJ,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;AACjG,CAAC;;AAED;AACA,OAAO,MAAMI,SAAS,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACF,MAAMhD,cAAc,CAAC2B,EAAE,CAAC;IACxBgB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;EAClD;AACF,CAAC;AAED,OAAO,MAAMI,QAAQ,GAAG,MAAAA,CAAA,KAAY;EAClC,IAAI;IACF,MAAMlD,aAAa,CAAC4B,EAAE,CAAC;IACvBgB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;EACjD;AACF,CAAC;AAED,eAAexB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}