{"hosting": {"public": "cyber-pos-system/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/static/**", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}, "firestore": {"rules": "firestore.rules.dev", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}