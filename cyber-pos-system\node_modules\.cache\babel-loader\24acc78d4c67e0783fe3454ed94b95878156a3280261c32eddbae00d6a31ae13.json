{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}], [\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"15mx69\"\n}], [\"path\", {\n  d: \"M20 10c-2 2-3 3.5-3 6\",\n  key: \"f35dl0\"\n}]];\nconst Heading6 = createLucideIcon(\"heading-6\", __iconNode);\nexport { __iconNode, Heading6 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Heading6", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\heading-6.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 12h8', key: '17cfdx' }],\n  ['path', { d: 'M4 18V6', key: '1rz3zl' }],\n  ['path', { d: 'M12 18V6', key: 'zqpxq5' }],\n  ['circle', { cx: '19', cy: '16', r: '2', key: '15mx69' }],\n  ['path', { d: 'M20 10c-2 2-3 3.5-3 6', key: 'f35dl0' }],\n];\n\n/**\n * @component @name Heading6\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMmg4IiAvPgogIDxwYXRoIGQ9Ik00IDE4VjYiIC8+CiAgPHBhdGggZD0iTTEyIDE4VjYiIC8+CiAgPGNpcmNsZSBjeD0iMTkiIGN5PSIxNiIgcj0iMiIgLz4KICA8cGF0aCBkPSJNMjAgMTBjLTIgMi0zIDMuNS0zIDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/heading-6\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heading6 = createLucideIcon('heading-6', __iconNode);\n\nexport default Heading6;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAU,GACxD;AAaM,MAAAI,QAAA,GAAWC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}