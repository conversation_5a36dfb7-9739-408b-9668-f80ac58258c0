{"ast": null, "code": "export const generateReceiptHTML = receiptData => {\n  const {\n    transaction,\n    attendant,\n    businessInfo\n  } = receiptData;\n  const receiptDate = transaction.createdAt.toLocaleString();\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Receipt - ${transaction.id}</title>\n      <style>\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n        \n        body {\n          font-family: 'Courier New', monospace;\n          font-size: 12px;\n          line-height: 1.4;\n          max-width: 300px;\n          margin: 0 auto;\n          padding: 10px;\n        }\n        \n        .header {\n          text-align: center;\n          border-bottom: 2px solid #000;\n          padding-bottom: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .business-name {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .business-info {\n          font-size: 10px;\n          margin-bottom: 2px;\n        }\n        \n        .receipt-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 15px;\n        }\n        \n        .items-table th,\n        .items-table td {\n          text-align: left;\n          padding: 2px 0;\n          border-bottom: 1px dotted #ccc;\n        }\n        \n        .items-table th {\n          font-weight: bold;\n          border-bottom: 1px solid #000;\n        }\n        \n        .item-name {\n          width: 60%;\n        }\n        \n        .item-qty {\n          width: 15%;\n          text-align: center;\n        }\n        \n        .item-price {\n          width: 25%;\n          text-align: right;\n        }\n        \n        .totals {\n          border-top: 2px solid #000;\n          padding-top: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .total-line {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 3px;\n        }\n        \n        .total-line.final {\n          font-weight: bold;\n          font-size: 14px;\n          border-top: 1px solid #000;\n          padding-top: 5px;\n          margin-top: 5px;\n        }\n        \n        .payment-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .footer {\n          text-align: center;\n          border-top: 1px dotted #ccc;\n          padding-top: 10px;\n          font-size: 10px;\n        }\n        \n        .print-button {\n          background: #007bff;\n          color: white;\n          border: none;\n          padding: 10px 20px;\n          border-radius: 5px;\n          cursor: pointer;\n          margin: 20px auto;\n          display: block;\n        }\n        \n        .print-button:hover {\n          background: #0056b3;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div class=\"business-name\">${businessInfo.name}</div>\n        <div class=\"business-info\">${businessInfo.address}</div>\n        <div class=\"business-info\">Tel: ${businessInfo.phone}</div>\n        ${businessInfo.email ? `<div class=\"business-info\">Email: ${businessInfo.email}</div>` : ''}\n      </div>\n      \n      <div class=\"receipt-info\">\n        <div><strong>Receipt #:</strong> ${transaction.id.substring(0, 8).toUpperCase()}</div>\n        <div><strong>Date:</strong> ${receiptDate}</div>\n        <div><strong>Attendant:</strong> ${attendant.name}</div>\n        ${transaction.customerId ? `<div><strong>Customer:</strong> Credit Sale</div>` : ''}\n      </div>\n      \n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th class=\"item-name\">Item</th>\n            <th class=\"item-qty\">Qty</th>\n            <th class=\"item-price\">Amount</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${transaction.items.map(item => `\n            <tr>\n              <td class=\"item-name\">\n                ${item.name}\n                ${item.notes ? `<br><small style=\"font-size: 9px; color: #666;\">${item.notes}</small>` : ''}\n              </td>\n              <td class=\"item-qty\">${item.quantity}</td>\n              <td class=\"item-price\">KSh ${item.totalPrice.toLocaleString()}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n      \n      <div class=\"totals\">\n        <div class=\"total-line\">\n          <span>Subtotal:</span>\n          <span>KSh ${transaction.subtotal.toLocaleString()}</span>\n        </div>\n        ${transaction.discount && transaction.discount > 0 ? `\n          <div class=\"total-line\">\n            <span>Discount:</span>\n            <span>-KSh ${transaction.discount.toLocaleString()}</span>\n          </div>\n        ` : ''}\n        <div class=\"total-line final\">\n          <span>TOTAL:</span>\n          <span>KSh ${transaction.total.toLocaleString()}</span>\n        </div>\n      </div>\n      \n      <div class=\"payment-info\">\n        <div><strong>Payment Method:</strong> ${getPaymentMethodName(transaction.paymentMethod)}</div>\n        ${transaction.notes ? `<div><strong>Notes:</strong> ${transaction.notes}</div>` : ''}\n      </div>\n      \n      <div class=\"footer\">\n        <div>Thank you for your business!</div>\n        <div>Visit us again soon</div>\n        <div style=\"margin-top: 10px; font-size: 9px;\">\n          Powered by Cyber POS System\n        </div>\n      </div>\n      \n      <button class=\"print-button no-print\" onclick=\"window.print()\">\n        Print Receipt\n      </button>\n      \n      <script>\n        // Auto-focus for printing\n        window.onload = function() {\n          window.focus();\n        };\n      </script>\n    </body>\n    </html>\n  `;\n};\nconst getPaymentMethodName = method => {\n  switch (method) {\n    case 'cash':\n      return 'Cash';\n    case 'mpesa':\n      return 'M-PESA';\n    case 'debt':\n      return 'Credit/Debt';\n    default:\n      return method;\n  }\n};\nexport const printReceipt = receiptData => {\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank', 'width=400,height=600');\n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n\n    // Wait for content to load then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print();\n      }, 250);\n    };\n  } else {\n    alert('Please allow popups to print receipts');\n  }\n};\nexport const downloadReceiptPDF = async receiptData => {\n  // This would require a PDF library like jsPDF or html2pdf\n  // For now, we'll just open the receipt in a new window\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank');\n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n  }\n};", "map": {"version": 3, "names": ["generateReceiptHTML", "receiptData", "transaction", "attendant", "businessInfo", "receiptDate", "createdAt", "toLocaleString", "id", "name", "address", "phone", "email", "substring", "toUpperCase", "customerId", "items", "map", "item", "notes", "quantity", "totalPrice", "join", "subtotal", "discount", "total", "getPaymentMethodName", "paymentMethod", "method", "printReceipt", "receiptHTML", "printWindow", "window", "open", "document", "write", "close", "onload", "setTimeout", "print", "alert", "downloadReceiptPDF"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/receiptGenerator.ts"], "sourcesContent": ["import { Transaction, User } from '../types';\n\nexport interface ReceiptData {\n  transaction: Transaction;\n  attendant: User;\n  businessInfo: {\n    name: string;\n    address: string;\n    phone: string;\n    email?: string;\n  };\n}\n\nexport const generateReceiptHTML = (receiptData: ReceiptData): string => {\n  const { transaction, attendant, businessInfo } = receiptData;\n  const receiptDate = transaction.createdAt.toLocaleString();\n\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Receipt - ${transaction.id}</title>\n      <style>\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n        \n        body {\n          font-family: 'Courier New', monospace;\n          font-size: 12px;\n          line-height: 1.4;\n          max-width: 300px;\n          margin: 0 auto;\n          padding: 10px;\n        }\n        \n        .header {\n          text-align: center;\n          border-bottom: 2px solid #000;\n          padding-bottom: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .business-name {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .business-info {\n          font-size: 10px;\n          margin-bottom: 2px;\n        }\n        \n        .receipt-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 15px;\n        }\n        \n        .items-table th,\n        .items-table td {\n          text-align: left;\n          padding: 2px 0;\n          border-bottom: 1px dotted #ccc;\n        }\n        \n        .items-table th {\n          font-weight: bold;\n          border-bottom: 1px solid #000;\n        }\n        \n        .item-name {\n          width: 60%;\n        }\n        \n        .item-qty {\n          width: 15%;\n          text-align: center;\n        }\n        \n        .item-price {\n          width: 25%;\n          text-align: right;\n        }\n        \n        .totals {\n          border-top: 2px solid #000;\n          padding-top: 10px;\n          margin-bottom: 15px;\n        }\n        \n        .total-line {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 3px;\n        }\n        \n        .total-line.final {\n          font-weight: bold;\n          font-size: 14px;\n          border-top: 1px solid #000;\n          padding-top: 5px;\n          margin-top: 5px;\n        }\n        \n        .payment-info {\n          margin-bottom: 15px;\n          font-size: 10px;\n        }\n        \n        .footer {\n          text-align: center;\n          border-top: 1px dotted #ccc;\n          padding-top: 10px;\n          font-size: 10px;\n        }\n        \n        .print-button {\n          background: #007bff;\n          color: white;\n          border: none;\n          padding: 10px 20px;\n          border-radius: 5px;\n          cursor: pointer;\n          margin: 20px auto;\n          display: block;\n        }\n        \n        .print-button:hover {\n          background: #0056b3;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div class=\"business-name\">${businessInfo.name}</div>\n        <div class=\"business-info\">${businessInfo.address}</div>\n        <div class=\"business-info\">Tel: ${businessInfo.phone}</div>\n        ${businessInfo.email ? `<div class=\"business-info\">Email: ${businessInfo.email}</div>` : ''}\n      </div>\n      \n      <div class=\"receipt-info\">\n        <div><strong>Receipt #:</strong> ${transaction.id.substring(0, 8).toUpperCase()}</div>\n        <div><strong>Date:</strong> ${receiptDate}</div>\n        <div><strong>Attendant:</strong> ${attendant.name}</div>\n        ${transaction.customerId ? `<div><strong>Customer:</strong> Credit Sale</div>` : ''}\n      </div>\n      \n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th class=\"item-name\">Item</th>\n            <th class=\"item-qty\">Qty</th>\n            <th class=\"item-price\">Amount</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${transaction.items.map(item => `\n            <tr>\n              <td class=\"item-name\">\n                ${item.name}\n                ${item.notes ? `<br><small style=\"font-size: 9px; color: #666;\">${item.notes}</small>` : ''}\n              </td>\n              <td class=\"item-qty\">${item.quantity}</td>\n              <td class=\"item-price\">KSh ${item.totalPrice.toLocaleString()}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n      \n      <div class=\"totals\">\n        <div class=\"total-line\">\n          <span>Subtotal:</span>\n          <span>KSh ${transaction.subtotal.toLocaleString()}</span>\n        </div>\n        ${transaction.discount && transaction.discount > 0 ? `\n          <div class=\"total-line\">\n            <span>Discount:</span>\n            <span>-KSh ${transaction.discount.toLocaleString()}</span>\n          </div>\n        ` : ''}\n        <div class=\"total-line final\">\n          <span>TOTAL:</span>\n          <span>KSh ${transaction.total.toLocaleString()}</span>\n        </div>\n      </div>\n      \n      <div class=\"payment-info\">\n        <div><strong>Payment Method:</strong> ${getPaymentMethodName(transaction.paymentMethod)}</div>\n        ${transaction.notes ? `<div><strong>Notes:</strong> ${transaction.notes}</div>` : ''}\n      </div>\n      \n      <div class=\"footer\">\n        <div>Thank you for your business!</div>\n        <div>Visit us again soon</div>\n        <div style=\"margin-top: 10px; font-size: 9px;\">\n          Powered by Cyber POS System\n        </div>\n      </div>\n      \n      <button class=\"print-button no-print\" onclick=\"window.print()\">\n        Print Receipt\n      </button>\n      \n      <script>\n        // Auto-focus for printing\n        window.onload = function() {\n          window.focus();\n        };\n      </script>\n    </body>\n    </html>\n  `;\n};\n\nconst getPaymentMethodName = (method: string): string => {\n  switch (method) {\n    case 'cash':\n      return 'Cash';\n    case 'mpesa':\n      return 'M-PESA';\n    case 'debt':\n      return 'Credit/Debt';\n    default:\n      return method;\n  }\n};\n\nexport const printReceipt = (receiptData: ReceiptData): void => {\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank', 'width=400,height=600');\n  \n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n    \n    // Wait for content to load then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print();\n      }, 250);\n    };\n  } else {\n    alert('Please allow popups to print receipts');\n  }\n};\n\nexport const downloadReceiptPDF = async (receiptData: ReceiptData): Promise<void> => {\n  // This would require a PDF library like jsPDF or html2pdf\n  // For now, we'll just open the receipt in a new window\n  const receiptHTML = generateReceiptHTML(receiptData);\n  const printWindow = window.open('', '_blank');\n  \n  if (printWindow) {\n    printWindow.document.write(receiptHTML);\n    printWindow.document.close();\n  }\n};\n"], "mappings": "AAaA,OAAO,MAAMA,mBAAmB,GAAIC,WAAwB,IAAa;EACvE,MAAM;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAGH,WAAW;EAC5D,MAAMI,WAAW,GAAGH,WAAW,CAACI,SAAS,CAACC,cAAc,CAAC,CAAC;EAE1D,OAAO;AACT;AACA;AACA;AACA;AACA,yBAAyBL,WAAW,CAACM,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqCJ,YAAY,CAACK,IAAI;AACtD,qCAAqCL,YAAY,CAACM,OAAO;AACzD,0CAA0CN,YAAY,CAACO,KAAK;AAC5D,UAAUP,YAAY,CAACQ,KAAK,GAAG,qCAAqCR,YAAY,CAACQ,KAAK,QAAQ,GAAG,EAAE;AACnG;AACA;AACA;AACA,2CAA2CV,WAAW,CAACM,EAAE,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACvF,sCAAsCT,WAAW;AACjD,2CAA2CF,SAAS,CAACM,IAAI;AACzD,UAAUP,WAAW,CAACa,UAAU,GAAG,mDAAmD,GAAG,EAAE;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYb,WAAW,CAACc,KAAK,CAACC,GAAG,CAACC,IAAI,IAAI;AAC1C;AACA;AACA,kBAAkBA,IAAI,CAACT,IAAI;AAC3B,kBAAkBS,IAAI,CAACC,KAAK,GAAG,mDAAmDD,IAAI,CAACC,KAAK,UAAU,GAAG,EAAE;AAC3G;AACA,qCAAqCD,IAAI,CAACE,QAAQ;AAClD,2CAA2CF,IAAI,CAACG,UAAU,CAACd,cAAc,CAAC,CAAC;AAC3E;AACA,WAAW,CAAC,CAACe,IAAI,CAAC,EAAE,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBpB,WAAW,CAACqB,QAAQ,CAAChB,cAAc,CAAC,CAAC;AAC3D;AACA,UAAUL,WAAW,CAACsB,QAAQ,IAAItB,WAAW,CAACsB,QAAQ,GAAG,CAAC,GAAG;AAC7D;AACA;AACA,yBAAyBtB,WAAW,CAACsB,QAAQ,CAACjB,cAAc,CAAC,CAAC;AAC9D;AACA,SAAS,GAAG,EAAE;AACd;AACA;AACA,sBAAsBL,WAAW,CAACuB,KAAK,CAAClB,cAAc,CAAC,CAAC;AACxD;AACA;AACA;AACA;AACA,gDAAgDmB,oBAAoB,CAACxB,WAAW,CAACyB,aAAa,CAAC;AAC/F,UAAUzB,WAAW,CAACiB,KAAK,GAAG,gCAAgCjB,WAAW,CAACiB,KAAK,QAAQ,GAAG,EAAE;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAED,MAAMO,oBAAoB,GAAIE,MAAc,IAAa;EACvD,QAAQA,MAAM;IACZ,KAAK,MAAM;MACT,OAAO,MAAM;IACf,KAAK,OAAO;MACV,OAAO,QAAQ;IACjB,KAAK,MAAM;MACT,OAAO,aAAa;IACtB;MACE,OAAOA,MAAM;EACjB;AACF,CAAC;AAED,OAAO,MAAMC,YAAY,GAAI5B,WAAwB,IAAW;EAC9D,MAAM6B,WAAW,GAAG9B,mBAAmB,CAACC,WAAW,CAAC;EACpD,MAAM8B,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,sBAAsB,CAAC;EAErE,IAAIF,WAAW,EAAE;IACfA,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACL,WAAW,CAAC;IACvCC,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC;;IAE5B;IACAL,WAAW,CAACM,MAAM,GAAG,MAAM;MACzBC,UAAU,CAAC,MAAM;QACfP,WAAW,CAACQ,KAAK,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;EACH,CAAC,MAAM;IACLC,KAAK,CAAC,uCAAuC,CAAC;EAChD;AACF,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAOxC,WAAwB,IAAoB;EACnF;EACA;EACA,MAAM6B,WAAW,GAAG9B,mBAAmB,CAACC,WAAW,CAAC;EACpD,MAAM8B,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;EAE7C,IAAIF,WAAW,EAAE;IACfA,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACL,WAAW,CAAC;IACvCC,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC;EAC9B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}