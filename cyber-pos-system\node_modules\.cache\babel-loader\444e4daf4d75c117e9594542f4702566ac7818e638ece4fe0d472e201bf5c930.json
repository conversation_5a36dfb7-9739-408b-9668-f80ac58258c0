{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.75 12h3.632a1 1 0 0 1 .894 1.447l-2.034 4.069a1 1 0 0 1-1.708.134l-2.124-2.97\",\n  key: \"ir91b5\"\n}], [\"path\", {\n  d: \"M17.106 9.053a1 1 0 0 1 .447 1.341l-3.106 6.211a1 1 0 0 1-1.342.447L3.61 12.3a2.92 2.92 0 0 1-1.3-3.91L3.69 5.6a2.92 2.92 0 0 1 3.92-1.3z\",\n  key: \"jlp8i1\"\n}], [\"path\", {\n  d: \"M2 19h3.76a2 2 0 0 0 1.8-1.1L9 15\",\n  key: \"19bib8\"\n}], [\"path\", {\n  d: \"M2 21v-4\",\n  key: \"l40lih\"\n}], [\"path\", {\n  d: \"M7 9h.01\",\n  key: \"19b3jx\"\n}]];\nconst Cctv = createLucideIcon(\"cctv\", __iconNode);\nexport { __iconNode, Cctv as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Cctv", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\cctv.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M16.75 12h3.632a1 1 0 0 1 .894 1.447l-2.034 4.069a1 1 0 0 1-1.708.134l-2.124-2.97',\n      key: 'ir91b5',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M17.106 9.053a1 1 0 0 1 .447 1.341l-3.106 6.211a1 1 0 0 1-1.342.447L3.61 12.3a2.92 2.92 0 0 1-1.3-3.91L3.69 5.6a2.92 2.92 0 0 1 3.92-1.3z',\n      key: 'jlp8i1',\n    },\n  ],\n  ['path', { d: 'M2 19h3.76a2 2 0 0 0 1.8-1.1L9 15', key: '19bib8' }],\n  ['path', { d: 'M2 21v-4', key: 'l40lih' }],\n  ['path', { d: 'M7 9h.01', key: '19b3jx' }],\n];\n\n/**\n * @component @name Cctv\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYuNzUgMTJoMy42MzJhMSAxIDAgMCAxIC44OTQgMS40NDdsLTIuMDM0IDQuMDY5YTEgMSAwIDAgMS0xLjcwOC4xMzRsLTIuMTI0LTIuOTciIC8+CiAgPHBhdGggZD0iTTE3LjEwNiA5LjA1M2ExIDEgMCAwIDEgLjQ0NyAxLjM0MWwtMy4xMDYgNi4yMTFhMSAxIDAgMCAxLTEuMzQyLjQ0N0wzLjYxIDEyLjNhMi45MiAyLjkyIDAgMCAxLTEuMy0zLjkxTDMuNjkgNS42YTIuOTIgMi45MiAwIDAgMSAzLjkyLTEuM3oiIC8+CiAgPHBhdGggZD0iTTIgMTloMy43NmEyIDIgMCAwIDAgMS44LTEuMUw5IDE1IiAvPgogIDxwYXRoIGQ9Ik0yIDIxdi00IiAvPgogIDxwYXRoIGQ9Ik03IDloLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cctv\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cctv = createLucideIcon('cctv', __iconNode);\n\nexport default Cctv;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,IAAA,GAAOC,gBAAiB,SAAQJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}