{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 7h-3a2 2 0 0 1-2-2V2\",\n  key: \"x099mo\"\n}], [\"path\", {\n  d: \"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z\",\n  key: \"18t6ie\"\n}], [\"path\", {\n  d: \"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8\",\n  key: \"1nja0z\"\n}]];\nconst Files = createLucideIcon(\"files\", __iconNode);\nexport { __iconNode, Files as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Files", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\files.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M20 7h-3a2 2 0 0 1-2-2V2', key: 'x099mo' }],\n  ['path', { d: 'M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z', key: '18t6ie' }],\n  ['path', { d: 'M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8', key: '1nja0z' }],\n];\n\n/**\n * @component @name Files\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgN2gtM2EyIDIgMCAwIDEtMi0yVjIiIC8+CiAgPHBhdGggZD0iTTkgMThhMiAyIDAgMCAxLTItMlY0YTIgMiAwIDAgMSAyLTJoN2w0IDR2MTBhMiAyIDAgMCAxLTIgMloiIC8+CiAgPHBhdGggZD0iTTMgNy42djEyLjhBMS42IDEuNiAwIDAgMCA0LjYgMjJoOS44IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/files\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Files = createLucideIcon('files', __iconNode);\n\nexport default Files;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,6DAA+D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAU,GACvE;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}