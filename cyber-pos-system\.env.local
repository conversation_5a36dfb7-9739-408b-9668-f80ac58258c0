# Firebase Configuration for Development
# Updated with actual Firebase service account configuration

# Firebase Project Configuration (from Firebase Console > Project Settings > Your apps)
# IMPORTANT: Replace these with your actual Firebase web app configuration
REACT_APP_FIREBASE_API_KEY=YOUR_ACTUAL_API_KEY_HERE
REACT_APP_FIREBASE_AUTH_DOMAIN=cyber-pos-system.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=cyber-pos-system
REACT_APP_FIREBASE_STORAGE_BUCKET=cyber-pos-system.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=YOUR_ACTUAL_SENDER_ID_HERE
REACT_APP_FIREBASE_APP_ID=YOUR_ACTUAL_APP_ID_HERE

# Firebase Service Account Configuration (for admin operations)
FIREBASE_PROJECT_ID=cyber-pos-system
FIREBASE_PRIVATE_KEY_ID=ba0bf70db7d90b9374430cf2be54ca8411c5c340
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDBGsDvQwbZPMmB\n04HiNYdKI/OZ1qgQGNpIKl+sR5QkNxmKZlaRRXpD5+S34/bC8Gg+SnonZfNI8mia\nyEODeR6vaV7qd8B5U1ilvFAgRoP8GWrUQUSkeSRALirk4RYrBpMO2h+gLAGMo1QQ\n92Du2kMkY37k8Vsg8x630/Zn5+5A4E3tGHCrwcu3UMjEFU6H/qFP/1oAOyheG4qB\nwIj3mxC95eSy2OePkp/xbI3k5ZERHmuhAF0p689ZMY42YfzAjq8MyA3KhzT5y7fP\nwh0U/KFsKocpFHVhkxKkciCnGDElRff5g8rfQVAnTc82YwU6F8ngwbLUTDdwcTTP\nyYtpMpQxAgMBAAECggEABAZoxtvywriwr70e7Wja+UWa/GYJZ64oRPM607UZaQYB\nKT26mHQrqR/GaFBZv9oMZt79IUDaY4xSTUahxKCTzjQgBhBesaYLeHHpSXuay5iO\niIkp6H6SGJM1x5f1qF+D1XOLD6GXMIKk3BEsowc85B9oS9adj7K4R/yPW76tfEqU\nm0xe/b+kkY5wW6T9K/fTemKhABFFBWJm5ngbyZMFDTHgF4kj3s2CdmzAnvgiHj0t\n2W6krg6vfCbQuvukZ4/aiYIHJYEMQtGCxeWgL85jn2bFay5eyM6oxYrbtgdDQFzy\nvFDPGr1XgpiNKpacgR9USiZXAFY/fn4ocjeuUC3XeQKBgQD88dLMgD3XwNnPQGwd\nG1d8ys3M7bZpeOBftPxyQogZFsYBnTO01i2790iqSRmIHOM+K+/ciPAZPPScXuNQ\nEMaMlLYhZCzU457T2Mm079t1vfWyMTQCcqbPBM1LyqgBys/1Uec+R6SfJ4rhM/NW\nb0Xr0pIB3WwfQtfByCES5Y8cyQKBgQDDb+M5V/WdpF2FASHgN4BOQ4Ej/wxgolu+\nz0tUbhmmuZLyCe2dnfBMuWArZ57Cxa8X0phvMar0zbr0xMlGLZkH+DvHbboQpySW\nrakArCOYxcTMe0sga1xw7/HudHtAdCcEaLbC8g9By4TpqE5SxdKEMSDVr4Oakri3\nzn/2a6M4KQKBgEN0Jbf9dwCDYFJhprM4aGNSHgHJXrzPzF4ZgLzjuzGdnh4uEuzl\ntPqMXmdy6QkgUwVwaB3Ssty4Ia9Vqp7eamRxAAT1lvMK5H8wL9I9qLPEfbIQZwNE\nGfHZIPpWR+l6cWzFc7prFp6Ox/14gisJr4bM97zhlITdqXX27cMBTuHpAoGAeGYX\nf542jBVUH7og2naWrqBbkIdxYp7jElu2Np9VYib05fND1VQVU87n9hVx8t/CDFUK\n34WjogiT4nLQ5FD287fza9qnQ9cUbVcyxF1H2LgNk0NpduFtI45OZu6Pd5eW6dpw\nrQxeZdQxX5j2F3wTQqjZZ4sm8jTtYx7uKisJdHECgYBkbzx9yWJZ5rSKaKLfYw7a\nlW8PuHD/A6M44RIEBXdI6mlVCgUflbdXQumB73C3vNZnCi40+KC7EK8HMZLSDQw3\nfLGKf0Ei3ZGSH7Rr9A/yIQL8qkHQ+9Km+qep13bShVuPy+8Sb2K9szElRxzdMC+z\n5BZwdovIT6Zny8FEQcQdLA==\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=117758163494852327626
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Environment
REACT_APP_ENVIRONMENT=development

# Firebase Emulator Configuration
REACT_APP_USE_FIREBASE_EMULATOR=false
REACT_APP_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# Note: This configuration now includes actual Firebase service account credentials
# For production builds, set REACT_APP_USE_FIREBASE_EMULATOR=false
# Keep the service account credentials secure and never commit them to public repositories
