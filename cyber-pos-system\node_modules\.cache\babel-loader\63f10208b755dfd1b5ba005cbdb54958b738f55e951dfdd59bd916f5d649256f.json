{"ast": null, "code": "import { collection, doc, setDoc, getDocs, getDoc, serverTimestamp } from 'firebase/firestore';\nimport { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';\nimport { auth, db } from '../config/firebase';\n// Demo users data\nconst demoUsers = [{\n  email: '<EMAIL>',\n  password: 'password',\n  name: 'System Administrator',\n  role: 'admin'\n}, {\n  email: '<EMAIL>',\n  password: 'password',\n  name: '<PERSON>',\n  role: 'attendant'\n}, {\n  email: '<EMAIL>',\n  password: 'password',\n  name: '<PERSON>',\n  role: 'technician'\n}];\n\n// Demo services data\nconst demoServices = [{\n  id: 'service-1',\n  name: 'Document Printing',\n  description: 'Black and white document printing',\n  basePrice: 10,\n  category: 'Printing',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  id: 'service-2',\n  name: 'Color Printing',\n  description: 'Color document printing',\n  basePrice: 20,\n  category: 'Printing',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  id: 'service-3',\n  name: 'Document Scanning',\n  description: 'Scan documents to PDF',\n  basePrice: 15,\n  category: 'Scanning',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  id: 'service-4',\n  name: 'Typing Services',\n  description: 'Document typing and formatting',\n  basePrice: 50,\n  category: 'Typing',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: ['service-5'] // Free emailing with typing\n}, {\n  id: 'service-5',\n  name: 'Email Services',\n  description: 'Send and receive emails',\n  basePrice: 20,\n  category: 'Internet',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  id: 'service-6',\n  name: 'KRA Services',\n  description: 'KRA PIN application and services',\n  basePrice: 100,\n  category: 'Government',\n  isActive: true,\n  allowPriceOverride: false,\n  bundledServices: []\n}, {\n  id: 'service-7',\n  name: 'Internet Browsing',\n  description: 'Internet access per hour',\n  basePrice: 30,\n  category: 'Internet',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}];\n\n// Demo products data\nconst demoProducts = [{\n  id: 'product-1',\n  name: 'A4 Paper (Ream)',\n  description: '500 sheets of A4 paper',\n  price: 450,\n  category: 'Paper',\n  stockQuantity: 25,\n  reorderLevel: 10,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-2',\n  name: 'Blue Pen',\n  description: 'Ballpoint pen - blue ink',\n  price: 20,\n  category: 'Writing',\n  stockQuantity: 50,\n  reorderLevel: 20,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-3',\n  name: 'Black Pen',\n  description: 'Ballpoint pen - black ink',\n  price: 20,\n  category: 'Writing',\n  stockQuantity: 45,\n  reorderLevel: 20,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-4',\n  name: 'Pencil',\n  description: 'HB pencil',\n  price: 15,\n  category: 'Writing',\n  stockQuantity: 30,\n  reorderLevel: 15,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-5',\n  name: 'Eraser',\n  description: 'White eraser',\n  price: 10,\n  category: 'Writing',\n  stockQuantity: 20,\n  reorderLevel: 10,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-6',\n  name: 'Stapler',\n  description: 'Small office stapler',\n  price: 150,\n  category: 'Office',\n  stockQuantity: 8,\n  reorderLevel: 5,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-7',\n  name: 'Staples (Box)',\n  description: 'Box of staples',\n  price: 50,\n  category: 'Office',\n  stockQuantity: 12,\n  reorderLevel: 5,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-8',\n  name: 'Envelope (Pack)',\n  description: 'Pack of 10 envelopes',\n  price: 80,\n  category: 'Paper',\n  stockQuantity: 15,\n  reorderLevel: 8,\n  hasExpiry: false,\n  isActive: true\n}, {\n  id: 'product-9',\n  name: 'Ink Cartridge',\n  description: 'Printer ink cartridge',\n  price: 2500,\n  category: 'Printer',\n  stockQuantity: 3,\n  reorderLevel: 5,\n  hasExpiry: true,\n  expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),\n  // 1 year from now\n  isActive: true\n}];\nexport const seedDemoData = async () => {\n  try {\n    console.log('Starting demo data seeding...');\n\n    // Check if data already exists\n    const usersSnapshot = await getDocs(collection(db, 'users'));\n    if (!usersSnapshot.empty) {\n      console.log('Demo data already exists, skipping seeding');\n      return;\n    }\n\n    // Seed services\n    console.log('Seeding services...');\n    for (const service of demoServices) {\n      await setDoc(doc(db, 'services', service.id), {\n        ...service,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n    }\n\n    // Seed products\n    console.log('Seeding products...');\n    for (const product of demoProducts) {\n      await setDoc(doc(db, 'products', product.id), {\n        ...product,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n        ...(product.hasExpiry && product.expiryDate ? {\n          expiryDate: product.expiryDate\n        } : {})\n      });\n    }\n    console.log('Demo data seeded successfully!');\n  } catch (error) {\n    console.error('Error seeding demo data:', error);\n    throw error;\n  }\n};\nexport const createDemoUsers = async () => {\n  try {\n    console.log('Creating demo users...');\n    for (const userData of demoUsers) {\n      try {\n        // Create Firebase user\n        const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);\n        const firebaseUser = userCredential.user;\n\n        // Update display name\n        await updateProfile(firebaseUser, {\n          displayName: userData.name\n        });\n\n        // Create user document in Firestore\n        await setDoc(doc(db, 'users', firebaseUser.uid), {\n          email: userData.email,\n          name: userData.name,\n          role: userData.role,\n          isActive: true,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp()\n        });\n        console.log(`Created user: ${userData.email}`);\n      } catch (error) {\n        if (error.code === 'auth/email-already-in-use') {\n          console.log(`User ${userData.email} already exists, skipping...`);\n        } else {\n          console.error(`Error creating user ${userData.email}:`, error);\n        }\n      }\n    }\n    console.log('Demo users created successfully!');\n  } catch (error) {\n    console.error('Error creating demo users:', error);\n    throw error;\n  }\n};\n\n// Create just the admin user for initial setup\nexport const createAdminUser = async () => {\n  try {\n    const adminData = demoUsers.find(user => user.role === 'admin');\n    if (!adminData) {\n      throw new Error('Admin user data not found');\n    }\n    console.log('Creating admin user...');\n\n    // Create Firebase user\n    const userCredential = await createUserWithEmailAndPassword(auth, adminData.email, adminData.password);\n    const firebaseUser = userCredential.user;\n\n    // Update display name\n    await updateProfile(firebaseUser, {\n      displayName: adminData.name\n    });\n\n    // Create user document in Firestore\n    await setDoc(doc(db, 'users', firebaseUser.uid), {\n      email: adminData.email,\n      name: adminData.name,\n      role: adminData.role,\n      isActive: true,\n      createdAt: serverTimestamp(),\n      updatedAt: serverTimestamp()\n    });\n    console.log(`Admin user created: ${adminData.email}`);\n  } catch (error) {\n    if (error.code === 'auth/email-already-in-use') {\n      console.log('Admin user already exists');\n    } else {\n      console.error('Error creating admin user:', error);\n      throw error;\n    }\n  }\n};\n\n// Function to initialize all demo data\nexport const initializeDemoData = async () => {\n  try {\n    // Check if we're using emulators or production\n    const isUsingEmulators = process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';\n    if (!isUsingEmulators) {\n      // For production Firebase, we need to be authenticated\n      if (!auth.currentUser) {\n        throw new Error('Authentication required for seeding data to production Firebase. Please login first or use Firebase emulators for development.');\n      }\n\n      // Check if current user has admin role\n      try {\n        const userDocRef = doc(db, 'users', auth.currentUser.uid);\n        const userSnapshot = await getDoc(userDocRef);\n        if (!userSnapshot.exists()) {\n          throw new Error('User document not found. Admin role required for seeding data to production Firebase.');\n        }\n        const userData = userSnapshot.data();\n        if (userData.role !== 'admin') {\n          throw new Error('Admin role required for seeding data to production Firebase.');\n        }\n      } catch (error) {\n        throw new Error('Unable to verify admin role. Admin role required for seeding data to production Firebase.');\n      }\n    }\n    await seedDemoData();\n    // Note: createDemoUsers should be called separately when needed\n    // as it requires authentication context\n  } catch (error) {\n    console.error('Error initializing demo data:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["collection", "doc", "setDoc", "getDocs", "getDoc", "serverTimestamp", "createUserWithEmailAndPassword", "updateProfile", "auth", "db", "demoUsers", "email", "password", "name", "role", "demoServices", "id", "description", "basePrice", "category", "isActive", "allowPriceOverride", "bundledServices", "demoProducts", "price", "stockQuantity", "reorderLevel", "hasEx<PERSON>ry", "expiryDate", "Date", "now", "seedDemoData", "console", "log", "usersSnapshot", "empty", "service", "createdAt", "updatedAt", "product", "error", "createDemoUsers", "userData", "userCredential", "firebaseUser", "user", "displayName", "uid", "code", "createAdminUser", "adminData", "find", "Error", "initializeDemoData", "isUsingEmulators", "process", "env", "REACT_APP_USE_FIREBASE_EMULATOR", "currentUser", "userDocRef", "userSnapshot", "exists", "data"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/seedData.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  setDoc,\n  getDocs,\n  getDoc,\n  serverTimestamp\n} from 'firebase/firestore';\nimport { \n  createUserWithEmailAndPassword, \n  updateProfile \n} from 'firebase/auth';\nimport { auth, db } from '../config/firebase';\nimport { User, Service, Product } from '../types';\n\n// Demo users data\nconst demoUsers = [\n  {\n    email: '<EMAIL>',\n    password: 'password',\n    name: 'System Administrator',\n    role: 'admin' as const\n  },\n  {\n    email: '<EMAIL>',\n    password: 'password',\n    name: '<PERSON>',\n    role: 'attendant' as const\n  },\n  {\n    email: '<EMAIL>',\n    password: 'password',\n    name: '<PERSON>',\n    role: 'technician' as const\n  }\n];\n\n// Demo services data\nconst demoServices = [\n  {\n    id: 'service-1',\n    name: 'Document Printing',\n    description: 'Black and white document printing',\n    basePrice: 10,\n    category: 'Printing',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: []\n  },\n  {\n    id: 'service-2',\n    name: 'Color Printing',\n    description: 'Color document printing',\n    basePrice: 20,\n    category: 'Printing',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: []\n  },\n  {\n    id: 'service-3',\n    name: 'Document Scanning',\n    description: 'Scan documents to PDF',\n    basePrice: 15,\n    category: 'Scanning',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: []\n  },\n  {\n    id: 'service-4',\n    name: 'Typing Services',\n    description: 'Document typing and formatting',\n    basePrice: 50,\n    category: 'Typing',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: ['service-5'] // Free emailing with typing\n  },\n  {\n    id: 'service-5',\n    name: 'Email Services',\n    description: 'Send and receive emails',\n    basePrice: 20,\n    category: 'Internet',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: []\n  },\n  {\n    id: 'service-6',\n    name: 'KRA Services',\n    description: 'KRA PIN application and services',\n    basePrice: 100,\n    category: 'Government',\n    isActive: true,\n    allowPriceOverride: false,\n    bundledServices: []\n  },\n  {\n    id: 'service-7',\n    name: 'Internet Browsing',\n    description: 'Internet access per hour',\n    basePrice: 30,\n    category: 'Internet',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: []\n  }\n];\n\n// Demo products data\nconst demoProducts = [\n  {\n    id: 'product-1',\n    name: 'A4 Paper (Ream)',\n    description: '500 sheets of A4 paper',\n    price: 450,\n    category: 'Paper',\n    stockQuantity: 25,\n    reorderLevel: 10,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-2',\n    name: 'Blue Pen',\n    description: 'Ballpoint pen - blue ink',\n    price: 20,\n    category: 'Writing',\n    stockQuantity: 50,\n    reorderLevel: 20,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-3',\n    name: 'Black Pen',\n    description: 'Ballpoint pen - black ink',\n    price: 20,\n    category: 'Writing',\n    stockQuantity: 45,\n    reorderLevel: 20,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-4',\n    name: 'Pencil',\n    description: 'HB pencil',\n    price: 15,\n    category: 'Writing',\n    stockQuantity: 30,\n    reorderLevel: 15,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-5',\n    name: 'Eraser',\n    description: 'White eraser',\n    price: 10,\n    category: 'Writing',\n    stockQuantity: 20,\n    reorderLevel: 10,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-6',\n    name: 'Stapler',\n    description: 'Small office stapler',\n    price: 150,\n    category: 'Office',\n    stockQuantity: 8,\n    reorderLevel: 5,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-7',\n    name: 'Staples (Box)',\n    description: 'Box of staples',\n    price: 50,\n    category: 'Office',\n    stockQuantity: 12,\n    reorderLevel: 5,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-8',\n    name: 'Envelope (Pack)',\n    description: 'Pack of 10 envelopes',\n    price: 80,\n    category: 'Paper',\n    stockQuantity: 15,\n    reorderLevel: 8,\n    hasExpiry: false,\n    isActive: true\n  },\n  {\n    id: 'product-9',\n    name: 'Ink Cartridge',\n    description: 'Printer ink cartridge',\n    price: 2500,\n    category: 'Printer',\n    stockQuantity: 3,\n    reorderLevel: 5,\n    hasExpiry: true,\n    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now\n    isActive: true\n  }\n];\n\nexport const seedDemoData = async (): Promise<void> => {\n  try {\n    console.log('Starting demo data seeding...');\n\n    // Check if data already exists\n    const usersSnapshot = await getDocs(collection(db, 'users'));\n    if (!usersSnapshot.empty) {\n      console.log('Demo data already exists, skipping seeding');\n      return;\n    }\n\n    // Seed services\n    console.log('Seeding services...');\n    for (const service of demoServices) {\n      await setDoc(doc(db, 'services', service.id), {\n        ...service,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n    }\n\n    // Seed products\n    console.log('Seeding products...');\n    for (const product of demoProducts) {\n      await setDoc(doc(db, 'products', product.id), {\n        ...product,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n        ...(product.hasExpiry && product.expiryDate ? { expiryDate: product.expiryDate } : {})\n      });\n    }\n\n    console.log('Demo data seeded successfully!');\n  } catch (error) {\n    console.error('Error seeding demo data:', error);\n    throw error;\n  }\n};\n\nexport const createDemoUsers = async (): Promise<void> => {\n  try {\n    console.log('Creating demo users...');\n\n    for (const userData of demoUsers) {\n      try {\n        // Create Firebase user\n        const userCredential = await createUserWithEmailAndPassword(\n          auth,\n          userData.email,\n          userData.password\n        );\n        const firebaseUser = userCredential.user;\n\n        // Update display name\n        await updateProfile(firebaseUser, { displayName: userData.name });\n\n        // Create user document in Firestore\n        await setDoc(doc(db, 'users', firebaseUser.uid), {\n          email: userData.email,\n          name: userData.name,\n          role: userData.role,\n          isActive: true,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp(),\n        });\n\n        console.log(`Created user: ${userData.email}`);\n      } catch (error: any) {\n        if (error.code === 'auth/email-already-in-use') {\n          console.log(`User ${userData.email} already exists, skipping...`);\n        } else {\n          console.error(`Error creating user ${userData.email}:`, error);\n        }\n      }\n    }\n\n    console.log('Demo users created successfully!');\n  } catch (error) {\n    console.error('Error creating demo users:', error);\n    throw error;\n  }\n};\n\n// Create just the admin user for initial setup\nexport const createAdminUser = async (): Promise<void> => {\n  try {\n    const adminData = demoUsers.find(user => user.role === 'admin');\n    if (!adminData) {\n      throw new Error('Admin user data not found');\n    }\n\n    console.log('Creating admin user...');\n\n    // Create Firebase user\n    const userCredential = await createUserWithEmailAndPassword(\n      auth,\n      adminData.email,\n      adminData.password\n    );\n    const firebaseUser = userCredential.user;\n\n    // Update display name\n    await updateProfile(firebaseUser, { displayName: adminData.name });\n\n    // Create user document in Firestore\n    await setDoc(doc(db, 'users', firebaseUser.uid), {\n      email: adminData.email,\n      name: adminData.name,\n      role: adminData.role,\n      isActive: true,\n      createdAt: serverTimestamp(),\n      updatedAt: serverTimestamp(),\n    });\n\n    console.log(`Admin user created: ${adminData.email}`);\n  } catch (error: any) {\n    if (error.code === 'auth/email-already-in-use') {\n      console.log('Admin user already exists');\n    } else {\n      console.error('Error creating admin user:', error);\n      throw error;\n    }\n  }\n};\n\n// Function to initialize all demo data\nexport const initializeDemoData = async (): Promise<void> => {\n  try {\n    // Check if we're using emulators or production\n    const isUsingEmulators = process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';\n\n    if (!isUsingEmulators) {\n      // For production Firebase, we need to be authenticated\n      if (!auth.currentUser) {\n        throw new Error('Authentication required for seeding data to production Firebase. Please login first or use Firebase emulators for development.');\n      }\n\n      // Check if current user has admin role\n      try {\n        const userDocRef = doc(db, 'users', auth.currentUser.uid);\n        const userSnapshot = await getDoc(userDocRef);\n\n        if (!userSnapshot.exists()) {\n          throw new Error('User document not found. Admin role required for seeding data to production Firebase.');\n        }\n\n        const userData = userSnapshot.data();\n        if (userData.role !== 'admin') {\n          throw new Error('Admin role required for seeding data to production Firebase.');\n        }\n      } catch (error) {\n        throw new Error('Unable to verify admin role. Admin role required for seeding data to production Firebase.');\n      }\n    }\n\n    await seedDemoData();\n    // Note: createDemoUsers should be called separately when needed\n    // as it requires authentication context\n  } catch (error) {\n    console.error('Error initializing demo data:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,SACEA,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,eAAe,QACV,oBAAoB;AAC3B,SACEC,8BAA8B,EAC9BC,aAAa,QACR,eAAe;AACtB,SAASC,IAAI,EAAEC,EAAE,QAAQ,oBAAoB;AAG7C;AACA,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,MAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,mBAAmB;EACzBI,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,gBAAgB;EACtBI,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,mBAAmB;EACzBI,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,iBAAiB;EACvBI,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC,EACD;EACEN,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,gBAAgB;EACtBI,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,cAAc;EACpBI,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,KAAK;EACzBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,mBAAmB;EACzBI,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,CACF;;AAED;AACA,MAAMC,YAAY,GAAG,CACnB;EACEP,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,iBAAiB;EACvBI,WAAW,EAAE,wBAAwB;EACrCO,KAAK,EAAE,GAAG;EACVL,QAAQ,EAAE,OAAO;EACjBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,UAAU;EAChBI,WAAW,EAAE,0BAA0B;EACvCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,SAAS;EACnBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,WAAW;EACjBI,WAAW,EAAE,2BAA2B;EACxCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,SAAS;EACnBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,QAAQ;EACdI,WAAW,EAAE,WAAW;EACxBO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,SAAS;EACnBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,QAAQ;EACdI,WAAW,EAAE,cAAc;EAC3BO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,SAAS;EACnBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,SAAS;EACfI,WAAW,EAAE,sBAAsB;EACnCO,KAAK,EAAE,GAAG;EACVL,QAAQ,EAAE,QAAQ;EAClBM,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,eAAe;EACrBI,WAAW,EAAE,gBAAgB;EAC7BO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,QAAQ;EAClBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,iBAAiB;EACvBI,WAAW,EAAE,sBAAsB;EACnCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,OAAO;EACjBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,WAAW;EACfH,IAAI,EAAE,eAAe;EACrBI,WAAW,EAAE,uBAAuB;EACpCO,KAAK,EAAE,IAAI;EACXL,QAAQ,EAAE,SAAS;EACnBM,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAAE;EAC9DV,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,OAAO,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAA2B;EACrD,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;IAE5C;IACA,MAAMC,aAAa,GAAG,MAAM/B,OAAO,CAACH,UAAU,CAACS,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5D,IAAI,CAACyB,aAAa,CAACC,KAAK,EAAE;MACxBH,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD;IACF;;IAEA;IACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,KAAK,MAAMG,OAAO,IAAIrB,YAAY,EAAE;MAClC,MAAMb,MAAM,CAACD,GAAG,CAACQ,EAAE,EAAE,UAAU,EAAE2B,OAAO,CAACpB,EAAE,CAAC,EAAE;QAC5C,GAAGoB,OAAO;QACVC,SAAS,EAAEhC,eAAe,CAAC,CAAC;QAC5BiC,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ;;IAEA;IACA2B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,KAAK,MAAMM,OAAO,IAAIhB,YAAY,EAAE;MAClC,MAAMrB,MAAM,CAACD,GAAG,CAACQ,EAAE,EAAE,UAAU,EAAE8B,OAAO,CAACvB,EAAE,CAAC,EAAE;QAC5C,GAAGuB,OAAO;QACVF,SAAS,EAAEhC,eAAe,CAAC,CAAC;QAC5BiC,SAAS,EAAEjC,eAAe,CAAC,CAAC;QAC5B,IAAIkC,OAAO,CAACZ,SAAS,IAAIY,OAAO,CAACX,UAAU,GAAG;UAAEA,UAAU,EAAEW,OAAO,CAACX;QAAW,CAAC,GAAG,CAAC,CAAC;MACvF,CAAC,CAAC;IACJ;IAEAI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAA2B;EACxD,IAAI;IACFT,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAErC,KAAK,MAAMS,QAAQ,IAAIhC,SAAS,EAAE;MAChC,IAAI;QACF;QACA,MAAMiC,cAAc,GAAG,MAAMrC,8BAA8B,CACzDE,IAAI,EACJkC,QAAQ,CAAC/B,KAAK,EACd+B,QAAQ,CAAC9B,QACX,CAAC;QACD,MAAMgC,YAAY,GAAGD,cAAc,CAACE,IAAI;;QAExC;QACA,MAAMtC,aAAa,CAACqC,YAAY,EAAE;UAAEE,WAAW,EAAEJ,QAAQ,CAAC7B;QAAK,CAAC,CAAC;;QAEjE;QACA,MAAMX,MAAM,CAACD,GAAG,CAACQ,EAAE,EAAE,OAAO,EAAEmC,YAAY,CAACG,GAAG,CAAC,EAAE;UAC/CpC,KAAK,EAAE+B,QAAQ,CAAC/B,KAAK;UACrBE,IAAI,EAAE6B,QAAQ,CAAC7B,IAAI;UACnBC,IAAI,EAAE4B,QAAQ,CAAC5B,IAAI;UACnBM,QAAQ,EAAE,IAAI;UACdiB,SAAS,EAAEhC,eAAe,CAAC,CAAC;UAC5BiC,SAAS,EAAEjC,eAAe,CAAC;QAC7B,CAAC,CAAC;QAEF2B,OAAO,CAACC,GAAG,CAAC,iBAAiBS,QAAQ,CAAC/B,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,OAAO6B,KAAU,EAAE;QACnB,IAAIA,KAAK,CAACQ,IAAI,KAAK,2BAA2B,EAAE;UAC9ChB,OAAO,CAACC,GAAG,CAAC,QAAQS,QAAQ,CAAC/B,KAAK,8BAA8B,CAAC;QACnE,CAAC,MAAM;UACLqB,OAAO,CAACQ,KAAK,CAAC,uBAAuBE,QAAQ,CAAC/B,KAAK,GAAG,EAAE6B,KAAK,CAAC;QAChE;MACF;IACF;IAEAR,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,eAAe,GAAG,MAAAA,CAAA,KAA2B;EACxD,IAAI;IACF,MAAMC,SAAS,GAAGxC,SAAS,CAACyC,IAAI,CAACN,IAAI,IAAIA,IAAI,CAAC/B,IAAI,KAAK,OAAO,CAAC;IAC/D,IAAI,CAACoC,SAAS,EAAE;MACd,MAAM,IAAIE,KAAK,CAAC,2BAA2B,CAAC;IAC9C;IAEApB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;;IAErC;IACA,MAAMU,cAAc,GAAG,MAAMrC,8BAA8B,CACzDE,IAAI,EACJ0C,SAAS,CAACvC,KAAK,EACfuC,SAAS,CAACtC,QACZ,CAAC;IACD,MAAMgC,YAAY,GAAGD,cAAc,CAACE,IAAI;;IAExC;IACA,MAAMtC,aAAa,CAACqC,YAAY,EAAE;MAAEE,WAAW,EAAEI,SAAS,CAACrC;IAAK,CAAC,CAAC;;IAElE;IACA,MAAMX,MAAM,CAACD,GAAG,CAACQ,EAAE,EAAE,OAAO,EAAEmC,YAAY,CAACG,GAAG,CAAC,EAAE;MAC/CpC,KAAK,EAAEuC,SAAS,CAACvC,KAAK;MACtBE,IAAI,EAAEqC,SAAS,CAACrC,IAAI;MACpBC,IAAI,EAAEoC,SAAS,CAACpC,IAAI;MACpBM,QAAQ,EAAE,IAAI;MACdiB,SAAS,EAAEhC,eAAe,CAAC,CAAC;MAC5BiC,SAAS,EAAEjC,eAAe,CAAC;IAC7B,CAAC,CAAC;IAEF2B,OAAO,CAACC,GAAG,CAAC,uBAAuBiB,SAAS,CAACvC,KAAK,EAAE,CAAC;EACvD,CAAC,CAAC,OAAO6B,KAAU,EAAE;IACnB,IAAIA,KAAK,CAACQ,IAAI,KAAK,2BAA2B,EAAE;MAC9ChB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,MAAM;MACLD,OAAO,CAACQ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,kBAAkB,GAAG,MAAAA,CAAA,KAA2B;EAC3D,IAAI;IACF;IACA,MAAMC,gBAAgB,GAAGC,OAAO,CAACC,GAAG,CAACC,+BAA+B,KAAK,MAAM;IAE/E,IAAI,CAACH,gBAAgB,EAAE;MACrB;MACA,IAAI,CAAC9C,IAAI,CAACkD,WAAW,EAAE;QACrB,MAAM,IAAIN,KAAK,CAAC,gIAAgI,CAAC;MACnJ;;MAEA;MACA,IAAI;QACF,MAAMO,UAAU,GAAG1D,GAAG,CAACQ,EAAE,EAAE,OAAO,EAAED,IAAI,CAACkD,WAAW,CAACX,GAAG,CAAC;QACzD,MAAMa,YAAY,GAAG,MAAMxD,MAAM,CAACuD,UAAU,CAAC;QAE7C,IAAI,CAACC,YAAY,CAACC,MAAM,CAAC,CAAC,EAAE;UAC1B,MAAM,IAAIT,KAAK,CAAC,uFAAuF,CAAC;QAC1G;QAEA,MAAMV,QAAQ,GAAGkB,YAAY,CAACE,IAAI,CAAC,CAAC;QACpC,IAAIpB,QAAQ,CAAC5B,IAAI,KAAK,OAAO,EAAE;UAC7B,MAAM,IAAIsC,KAAK,CAAC,8DAA8D,CAAC;QACjF;MACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACd,MAAM,IAAIY,KAAK,CAAC,2FAA2F,CAAC;MAC9G;IACF;IAEA,MAAMrB,YAAY,CAAC,CAAC;IACpB;IACA;EACF,CAAC,CAAC,OAAOS,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}