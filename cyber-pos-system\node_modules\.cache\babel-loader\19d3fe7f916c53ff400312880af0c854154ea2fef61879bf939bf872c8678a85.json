{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M4 7V4a2 2 0 0 1 2-2 2 2 0 0 0-2 2\",\n  key: \"1vk7w2\"\n}], [\"path\", {\n  d: \"M4.063 20.999a2 2 0 0 0 2 1L18 22a2 2 0 0 0 2-2V7l-5-5H6\",\n  key: \"1jink5\"\n}], [\"path\", {\n  d: \"m5 11-3 3\",\n  key: \"1dgrs4\"\n}], [\"path\", {\n  d: \"m5 17-3-3h10\",\n  key: \"1mvvaf\"\n}]];\nconst FileOutput = createLucideIcon(\"file-output\", __iconNode);\nexport { __iconNode, FileOutput as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FileOutput", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\file-output.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M4 7V4a2 2 0 0 1 2-2 2 2 0 0 0-2 2', key: '1vk7w2' }],\n  ['path', { d: 'M4.063 20.999a2 2 0 0 0 2 1L18 22a2 2 0 0 0 2-2V7l-5-5H6', key: '1jink5' }],\n  ['path', { d: 'm5 11-3 3', key: '1dgrs4' }],\n  ['path', { d: 'm5 17-3-3h10', key: '1mvvaf' }],\n];\n\n/**\n * @component @name FileOutput\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMnY0YTIgMiAwIDAgMCAyIDJoNCIgLz4KICA8cGF0aCBkPSJNNCA3VjRhMiAyIDAgMCAxIDItMiAyIDIgMCAwIDAtMiAyIiAvPgogIDxwYXRoIGQ9Ik00LjA2MyAyMC45OTlhMiAyIDAgMCAwIDIgMUwxOCAyMmEyIDIgMCAwIDAgMi0yVjdsLTUtNUg2IiAvPgogIDxwYXRoIGQ9Im01IDExLTMgMyIgLz4KICA8cGF0aCBkPSJtNSAxNy0zLTNoMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-output\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileOutput = createLucideIcon('file-output', __iconNode);\n\nexport default FileOutput;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAU,GAC/C;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}