{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 20v-7a4 4 0 0 0-4-4H4\",\n  key: \"1nkjon\"\n}], [\"path\", {\n  d: \"M9 14 4 9l5-5\",\n  key: \"102s5s\"\n}]];\nconst CornerUpLeft = createLucideIcon(\"corner-up-left\", __iconNode);\nexport { __iconNode, CornerUpLeft as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "CornerUpLeft", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\corner-up-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M20 20v-7a4 4 0 0 0-4-4H4', key: '1nk<PERSON>' }],\n  ['path', { d: 'M9 14 4 9l5-5', key: '102s5s' }],\n];\n\n/**\n * @component @name CornerUpLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMjB2LTdhNCA0IDAgMCAwLTQtNEg0IiAvPgogIDxwYXRoIGQ9Ik05IDE0IDQgOWw1LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/corner-up-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerUpLeft = createLucideIcon('corner-up-left', __iconNode);\n\nexport default CornerUpLeft;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAU,GAChD;AAaM,MAAAC,YAAA,GAAeC,gBAAiB,mBAAkBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}