import {
  collection,
  doc,
  setDoc,
  getDocs,
  getDoc,
  serverTimestamp
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword, 
  updateProfile 
} from 'firebase/auth';
import { auth, db } from '../config/firebase';
import { User, Service, Product } from '../types';

// Demo users data
const demoUsers = [
  {
    email: '<EMAIL>',
    password: 'password',
    name: 'System Administrator',
    role: 'admin' as const
  },
  {
    email: '<EMAIL>',
    password: 'password',
    name: '<PERSON>',
    role: 'attendant' as const
  },
  {
    email: '<EMAIL>',
    password: 'password',
    name: '<PERSON>',
    role: 'technician' as const
  }
];

// Demo services data
const demoServices = [
  {
    id: 'service-1',
    name: 'Document Printing',
    description: 'Black and white document printing',
    basePrice: 10,
    category: 'Printing',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: []
  },
  {
    id: 'service-2',
    name: 'Color Printing',
    description: 'Color document printing',
    basePrice: 20,
    category: 'Printing',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: []
  },
  {
    id: 'service-3',
    name: 'Document Scanning',
    description: 'Scan documents to PDF',
    basePrice: 15,
    category: 'Scanning',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: []
  },
  {
    id: 'service-4',
    name: 'Typing Services',
    description: 'Document typing and formatting',
    basePrice: 50,
    category: 'Typing',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: ['service-5'] // Free emailing with typing
  },
  {
    id: 'service-5',
    name: 'Email Services',
    description: 'Send and receive emails',
    basePrice: 20,
    category: 'Internet',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: []
  },
  {
    id: 'service-6',
    name: 'KRA Services',
    description: 'KRA PIN application and services',
    basePrice: 100,
    category: 'Government',
    isActive: true,
    allowPriceOverride: false,
    bundledServices: []
  },
  {
    id: 'service-7',
    name: 'Internet Browsing',
    description: 'Internet access per hour',
    basePrice: 30,
    category: 'Internet',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: []
  }
];

// Demo products data
const demoProducts = [
  {
    id: 'product-1',
    name: 'A4 Paper (Ream)',
    description: '500 sheets of A4 paper',
    price: 450,
    category: 'Paper',
    stockQuantity: 25,
    reorderLevel: 10,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-2',
    name: 'Blue Pen',
    description: 'Ballpoint pen - blue ink',
    price: 20,
    category: 'Writing',
    stockQuantity: 50,
    reorderLevel: 20,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-3',
    name: 'Black Pen',
    description: 'Ballpoint pen - black ink',
    price: 20,
    category: 'Writing',
    stockQuantity: 45,
    reorderLevel: 20,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-4',
    name: 'Pencil',
    description: 'HB pencil',
    price: 15,
    category: 'Writing',
    stockQuantity: 30,
    reorderLevel: 15,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-5',
    name: 'Eraser',
    description: 'White eraser',
    price: 10,
    category: 'Writing',
    stockQuantity: 20,
    reorderLevel: 10,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-6',
    name: 'Stapler',
    description: 'Small office stapler',
    price: 150,
    category: 'Office',
    stockQuantity: 8,
    reorderLevel: 5,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-7',
    name: 'Staples (Box)',
    description: 'Box of staples',
    price: 50,
    category: 'Office',
    stockQuantity: 12,
    reorderLevel: 5,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-8',
    name: 'Envelope (Pack)',
    description: 'Pack of 10 envelopes',
    price: 80,
    category: 'Paper',
    stockQuantity: 15,
    reorderLevel: 8,
    hasExpiry: false,
    isActive: true
  },
  {
    id: 'product-9',
    name: 'Ink Cartridge',
    description: 'Printer ink cartridge',
    price: 2500,
    category: 'Printer',
    stockQuantity: 3,
    reorderLevel: 5,
    hasExpiry: true,
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    isActive: true
  }
];

export const seedDemoData = async (): Promise<void> => {
  try {
    console.log('Starting demo data seeding...');

    // Check if data already exists
    const usersSnapshot = await getDocs(collection(db, 'users'));
    if (!usersSnapshot.empty) {
      console.log('Demo data already exists, skipping seeding');
      return;
    }

    // Seed services
    console.log('Seeding services...');
    for (const service of demoServices) {
      await setDoc(doc(db, 'services', service.id), {
        ...service,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }

    // Seed products
    console.log('Seeding products...');
    for (const product of demoProducts) {
      await setDoc(doc(db, 'products', product.id), {
        ...product,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        ...(product.hasExpiry && product.expiryDate ? { expiryDate: product.expiryDate } : {})
      });
    }

    console.log('Demo data seeded successfully!');
  } catch (error) {
    console.error('Error seeding demo data:', error);
    throw error;
  }
};

export const createDemoUsers = async (): Promise<void> => {
  try {
    console.log('Creating demo users...');

    for (const userData of demoUsers) {
      try {
        // Create Firebase user
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          userData.email,
          userData.password
        );
        const firebaseUser = userCredential.user;

        // Update display name
        await updateProfile(firebaseUser, { displayName: userData.name });

        // Create user document in Firestore
        await setDoc(doc(db, 'users', firebaseUser.uid), {
          email: userData.email,
          name: userData.name,
          role: userData.role,
          isActive: true,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        console.log(`Created user: ${userData.email}`);
      } catch (error: any) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`User ${userData.email} already exists, skipping...`);
        } else {
          console.error(`Error creating user ${userData.email}:`, error);
        }
      }
    }

    console.log('Demo users created successfully!');
  } catch (error) {
    console.error('Error creating demo users:', error);
    throw error;
  }
};

// Create just the admin user for initial setup
export const createAdminUser = async (): Promise<void> => {
  try {
    const adminData = demoUsers.find(user => user.role === 'admin');
    if (!adminData) {
      throw new Error('Admin user data not found');
    }

    console.log('Creating admin user...');

    // Create Firebase user
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      adminData.email,
      adminData.password
    );
    const firebaseUser = userCredential.user;

    // Update display name
    await updateProfile(firebaseUser, { displayName: adminData.name });

    // Create user document in Firestore
    await setDoc(doc(db, 'users', firebaseUser.uid), {
      email: adminData.email,
      name: adminData.name,
      role: adminData.role,
      isActive: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    console.log(`Admin user created: ${adminData.email}`);
  } catch (error: any) {
    if (error.code === 'auth/email-already-in-use') {
      console.log('Admin user already exists');
    } else {
      console.error('Error creating admin user:', error);
      throw error;
    }
  }
};

// Function to initialize all demo data
export const initializeDemoData = async (): Promise<void> => {
  try {
    // Check if we're using emulators or production
    const isUsingEmulators = process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';

    if (!isUsingEmulators) {
      // For production Firebase, we need to be authenticated
      if (!auth.currentUser) {
        throw new Error('Authentication required for seeding data to production Firebase. Please login first or use Firebase emulators for development.');
      }

      // Check if current user has admin role
      try {
        const userDocRef = doc(db, 'users', auth.currentUser.uid);
        const userSnapshot = await getDoc(userDocRef);

        if (!userSnapshot.exists()) {
          throw new Error('User document not found. Admin role required for seeding data to production Firebase.');
        }

        const userData = userSnapshot.data();
        if (userData.role !== 'admin') {
          throw new Error('Admin role required for seeding data to production Firebase.');
        }
      } catch (error) {
        throw new Error('Unable to verify admin role. Admin role required for seeding data to production Firebase.');
      }
    }

    await seedDemoData();
    // Note: createDemoUsers should be called separately when needed
    // as it requires authentication context
  } catch (error) {
    console.error('Error initializing demo data:', error);
    throw error;
  }
};
