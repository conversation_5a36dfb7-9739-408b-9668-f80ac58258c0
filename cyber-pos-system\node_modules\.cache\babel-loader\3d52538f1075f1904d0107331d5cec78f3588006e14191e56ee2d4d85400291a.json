{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\inventory\\\\StockAdjustmentModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { X, Package, Plus, Minus, RefreshCw, AlertTriangle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StockAdjustmentModal = ({\n  product,\n  onClose,\n  onAdjust\n}) => {\n  _s();\n  const [adjustmentType, setAdjustmentType] = useState('set');\n  const [adjustmentValue, setAdjustmentValue] = useState(0);\n  const [reason, setReason] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const calculateNewQuantity = () => {\n    switch (adjustmentType) {\n      case 'set':\n        return adjustmentValue;\n      case 'add':\n        return product.stockQuantity + adjustmentValue;\n      case 'subtract':\n        return Math.max(0, product.stockQuantity - adjustmentValue);\n      default:\n        return product.stockQuantity;\n    }\n  };\n  const newQuantity = calculateNewQuantity();\n  const difference = newQuantity - product.stockQuantity;\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n    try {\n      if (newQuantity < 0) {\n        throw new Error('Stock quantity cannot be negative');\n      }\n      await onAdjust(product.id, newQuantity);\n      onClose();\n    } catch (error) {\n      setError(error.message || 'Failed to adjust stock');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const adjustmentTypes = [{\n    id: 'set',\n    name: 'Set To',\n    description: 'Set stock to exact amount',\n    icon: RefreshCw\n  }, {\n    id: 'add',\n    name: 'Add Stock',\n    description: 'Add to current stock',\n    icon: Plus\n  }, {\n    id: 'subtract',\n    name: 'Remove Stock',\n    description: 'Remove from current stock',\n    icon: Minus\n  }];\n  const commonReasons = ['Stock received', 'Stock sold', 'Stock damaged', 'Stock expired', 'Stock returned', 'Inventory count correction', 'Other'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Package, {\n              className: \"h-5 w-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), \"Adjust Stock\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 p-4 rounded-lg mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Current Stock:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-900\",\n              children: [product.stockQuantity, \" units\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Reorder Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-900\",\n              children: [product.reorderLevel, \" units\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-3\",\n              children: \"Adjustment Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-3\",\n              children: adjustmentTypes.map(type => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: `relative flex cursor-pointer rounded-lg border p-3 focus:outline-none ${adjustmentType === type.id ? 'border-primary-600 ring-2 ring-primary-600' : 'border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"adjustment-type\",\n                  value: type.id,\n                  checked: adjustmentType === type.id,\n                  onChange: e => setAdjustmentType(e.target.value),\n                  className: \"sr-only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(type.icon, {\n                    className: `h-5 w-5 mr-3 ${adjustmentType === type.id ? 'text-primary-600' : 'text-gray-400'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-sm font-medium ${adjustmentType === type.id ? 'text-primary-900' : 'text-gray-900'}`,\n                      children: type.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: type.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this), adjustmentType === type.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -inset-px rounded-lg border-2 border-primary-600 pointer-events-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 23\n                }, this)]\n              }, type.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: adjustmentType === 'set' ? 'New Stock Quantity' : adjustmentType === 'add' ? 'Quantity to Add' : 'Quantity to Remove'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              required: true,\n              min: \"0\",\n              value: adjustmentValue,\n              onChange: e => setAdjustmentValue(parseInt(e.target.value) || 0),\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n              placeholder: \"Enter quantity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-blue-900 mb-2\",\n              children: \"Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-700\",\n                  children: \"Current Stock:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-blue-900\",\n                  children: [product.stockQuantity, \" units\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-700\",\n                  children: \"New Stock:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-blue-900\",\n                  children: [newQuantity, \" units\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-700\",\n                  children: \"Change:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${difference > 0 ? 'text-green-600' : difference < 0 ? 'text-red-600' : 'text-blue-900'}`,\n                  children: [difference > 0 ? '+' : '', difference, \" units\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), newQuantity <= product.reorderLevel && newQuantity > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-4 w-4 text-orange-500 mt-0.5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-orange-700\",\n                children: [\"New stock level is below reorder threshold (\", product.reorderLevel, \" units)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), newQuantity === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-4 w-4 text-red-500 mt-0.5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-red-700\",\n                children: \"This will mark the product as out of stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Reason for Adjustment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: reason,\n              onChange: e => setReason(e.target.value),\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a reason\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), commonReasons.map(reasonOption => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: reasonOption,\n                children: reasonOption\n              }, reasonOption, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), reason === 'Other' && /*#__PURE__*/_jsxDEV(\"textarea\", {\n              placeholder: \"Please specify the reason...\",\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n              rows: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 pt-6 border-t\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: onClose,\n              className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading || adjustmentValue < 0 || adjustmentType === 'subtract' && adjustmentValue > product.stockQuantity,\n              className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: loading ? 'Adjusting...' : 'Adjust Stock'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(StockAdjustmentModal, \"t3uqlOq8MGVbCsuH5ygpbeWeiN0=\");\n_c = StockAdjustmentModal;\nexport default StockAdjustmentModal;\nvar _c;\n$RefreshReg$(_c, \"StockAdjustmentModal\");", "map": {"version": 3, "names": ["React", "useState", "X", "Package", "Plus", "Minus", "RefreshCw", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "StockAdjustmentModal", "product", "onClose", "onAdjust", "_s", "adjustmentType", "setAdjustmentType", "adjustmentValue", "setAdjustmentValue", "reason", "setReason", "loading", "setLoading", "error", "setError", "calculateNewQuantity", "stockQuantity", "Math", "max", "newQuantity", "difference", "handleSubmit", "e", "preventDefault", "Error", "id", "message", "adjustmentTypes", "name", "description", "icon", "commonReasons", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "reorderLevel", "onSubmit", "map", "type", "value", "checked", "onChange", "target", "required", "min", "parseInt", "placeholder", "reasonOption", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/StockAdjustmentModal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { X, Package, Plus, Minus, RefreshCw, AlertTriangle } from 'lucide-react';\nimport { Product } from '../../types';\n\ninterface StockAdjustmentModalProps {\n  product: Product;\n  onClose: () => void;\n  onAdjust: (productId: string, newQuantity: number) => Promise<void>;\n}\n\nconst StockAdjustmentModal: React.FC<StockAdjustmentModalProps> = ({\n  product,\n  onClose,\n  onAdjust\n}) => {\n  const [adjustmentType, setAdjustmentType] = useState<'set' | 'add' | 'subtract'>('set');\n  const [adjustmentValue, setAdjustmentValue] = useState(0);\n  const [reason, setReason] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const calculateNewQuantity = () => {\n    switch (adjustmentType) {\n      case 'set':\n        return adjustmentValue;\n      case 'add':\n        return product.stockQuantity + adjustmentValue;\n      case 'subtract':\n        return Math.max(0, product.stockQuantity - adjustmentValue);\n      default:\n        return product.stockQuantity;\n    }\n  };\n\n  const newQuantity = calculateNewQuantity();\n  const difference = newQuantity - product.stockQuantity;\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      if (newQuantity < 0) {\n        throw new Error('Stock quantity cannot be negative');\n      }\n\n      await onAdjust(product.id, newQuantity);\n      onClose();\n    } catch (error: any) {\n      setError(error.message || 'Failed to adjust stock');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const adjustmentTypes = [\n    {\n      id: 'set' as const,\n      name: 'Set To',\n      description: 'Set stock to exact amount',\n      icon: RefreshCw\n    },\n    {\n      id: 'add' as const,\n      name: 'Add Stock',\n      description: 'Add to current stock',\n      icon: Plus\n    },\n    {\n      id: 'subtract' as const,\n      name: 'Remove Stock',\n      description: 'Remove from current stock',\n      icon: Minus\n    }\n  ];\n\n  const commonReasons = [\n    'Stock received',\n    'Stock sold',\n    'Stock damaged',\n    'Stock expired',\n    'Stock returned',\n    'Inventory count correction',\n    'Other'\n  ];\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n              <Package className=\"h-5 w-5 mr-2\" />\n              Adjust Stock\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          </div>\n\n          {/* Product Info */}\n          <div className=\"bg-gray-50 p-4 rounded-lg mb-6\">\n            <h4 className=\"font-medium text-gray-900\">{product.name}</h4>\n            <p className=\"text-sm text-gray-600\">{product.description}</p>\n            <div className=\"mt-2 flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Current Stock:</span>\n              <span className=\"font-semibold text-gray-900\">{product.stockQuantity} units</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Reorder Level:</span>\n              <span className=\"font-semibold text-gray-900\">{product.reorderLevel} units</span>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Adjustment Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Adjustment Type\n              </label>\n              <div className=\"grid grid-cols-1 gap-3\">\n                {adjustmentTypes.map((type) => (\n                  <label\n                    key={type.id}\n                    className={`relative flex cursor-pointer rounded-lg border p-3 focus:outline-none ${\n                      adjustmentType === type.id\n                        ? 'border-primary-600 ring-2 ring-primary-600'\n                        : 'border-gray-300'\n                    }`}\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"adjustment-type\"\n                      value={type.id}\n                      checked={adjustmentType === type.id}\n                      onChange={(e) => setAdjustmentType(e.target.value as any)}\n                      className=\"sr-only\"\n                    />\n                    <div className=\"flex items-center\">\n                      <type.icon className={`h-5 w-5 mr-3 ${\n                        adjustmentType === type.id ? 'text-primary-600' : 'text-gray-400'\n                      }`} />\n                      <div>\n                        <span className={`text-sm font-medium ${\n                          adjustmentType === type.id ? 'text-primary-900' : 'text-gray-900'\n                        }`}>\n                          {type.name}\n                        </span>\n                        <p className=\"text-xs text-gray-500\">{type.description}</p>\n                      </div>\n                    </div>\n                    {adjustmentType === type.id && (\n                      <div className=\"absolute -inset-px rounded-lg border-2 border-primary-600 pointer-events-none\" />\n                    )}\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Adjustment Value */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                {adjustmentType === 'set' ? 'New Stock Quantity' : \n                 adjustmentType === 'add' ? 'Quantity to Add' : 'Quantity to Remove'}\n              </label>\n              <input\n                type=\"number\"\n                required\n                min=\"0\"\n                value={adjustmentValue}\n                onChange={(e) => setAdjustmentValue(parseInt(e.target.value) || 0)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"Enter quantity\"\n              />\n            </div>\n\n            {/* Preview */}\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-900 mb-2\">Preview</h4>\n              <div className=\"space-y-1 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-blue-700\">Current Stock:</span>\n                  <span className=\"font-medium text-blue-900\">{product.stockQuantity} units</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-blue-700\">New Stock:</span>\n                  <span className=\"font-medium text-blue-900\">{newQuantity} units</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-blue-700\">Change:</span>\n                  <span className={`font-medium ${\n                    difference > 0 ? 'text-green-600' : difference < 0 ? 'text-red-600' : 'text-blue-900'\n                  }`}>\n                    {difference > 0 ? '+' : ''}{difference} units\n                  </span>\n                </div>\n              </div>\n              \n              {/* Warnings */}\n              {newQuantity <= product.reorderLevel && newQuantity > 0 && (\n                <div className=\"mt-3 flex items-start\">\n                  <AlertTriangle className=\"h-4 w-4 text-orange-500 mt-0.5 mr-2\" />\n                  <span className=\"text-xs text-orange-700\">\n                    New stock level is below reorder threshold ({product.reorderLevel} units)\n                  </span>\n                </div>\n              )}\n              \n              {newQuantity === 0 && (\n                <div className=\"mt-3 flex items-start\">\n                  <AlertTriangle className=\"h-4 w-4 text-red-500 mt-0.5 mr-2\" />\n                  <span className=\"text-xs text-red-700\">\n                    This will mark the product as out of stock\n                  </span>\n                </div>\n              )}\n            </div>\n\n            {/* Reason */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Reason for Adjustment\n              </label>\n              <select\n                value={reason}\n                onChange={(e) => setReason(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500 mb-2\"\n              >\n                <option value=\"\">Select a reason</option>\n                {commonReasons.map(reasonOption => (\n                  <option key={reasonOption} value={reasonOption}>{reasonOption}</option>\n                ))}\n              </select>\n              {reason === 'Other' && (\n                <textarea\n                  placeholder=\"Please specify the reason...\"\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  rows={2}\n                />\n              )}\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading || adjustmentValue < 0 || (adjustmentType === 'subtract' && adjustmentValue > product.stockQuantity)}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Adjusting...' : 'Adjust Stock'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StockAdjustmentModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,CAAC,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASjF,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAA6B,KAAK,CAAC;EACvF,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMwB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQV,cAAc;MACpB,KAAK,KAAK;QACR,OAAOE,eAAe;MACxB,KAAK,KAAK;QACR,OAAON,OAAO,CAACe,aAAa,GAAGT,eAAe;MAChD,KAAK,UAAU;QACb,OAAOU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjB,OAAO,CAACe,aAAa,GAAGT,eAAe,CAAC;MAC7D;QACE,OAAON,OAAO,CAACe,aAAa;IAChC;EACF,CAAC;EAED,MAAMG,WAAW,GAAGJ,oBAAoB,CAAC,CAAC;EAC1C,MAAMK,UAAU,GAAGD,WAAW,GAAGlB,OAAO,CAACe,aAAa;EAEtD,MAAMK,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBT,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAIO,WAAW,GAAG,CAAC,EAAE;QACnB,MAAM,IAAIK,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEA,MAAMrB,QAAQ,CAACF,OAAO,CAACwB,EAAE,EAAEN,WAAW,CAAC;MACvCjB,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOW,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAACa,OAAO,IAAI,wBAAwB,CAAC;IACrD,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,eAAe,GAAG,CACtB;IACEF,EAAE,EAAE,KAAc;IAClBG,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAElC;EACR,CAAC,EACD;IACE6B,EAAE,EAAE,KAAc;IAClBG,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAEpC;EACR,CAAC,EACD;IACE+B,EAAE,EAAE,UAAmB;IACvBG,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAEnC;EACR,CAAC,CACF;EAED,MAAMoC,aAAa,GAAG,CACpB,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,eAAe,EACf,gBAAgB,EAChB,4BAA4B,EAC5B,OAAO,CACR;EAED,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFlC,OAAA;MAAKiC,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/FlC,OAAA;QAAKiC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBlC,OAAA;UAAKiC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDlC,OAAA;YAAIiC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBACjElC,OAAA,CAACN,OAAO;cAACuC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtC,OAAA;YACEuC,OAAO,EAAEpC,OAAQ;YACjB8B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7ClC,OAAA,CAACP,CAAC;cAACwC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtC,OAAA;UAAKiC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7ClC,OAAA;YAAIiC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEhC,OAAO,CAAC2B;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7DtC,OAAA;YAAGiC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEhC,OAAO,CAAC4B;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DtC,OAAA;YAAKiC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlC,OAAA;cAAMiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DtC,OAAA;cAAMiC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAEhC,OAAO,CAACe,aAAa,EAAC,QAAM;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNtC,OAAA;YAAKiC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlC,OAAA;cAAMiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DtC,OAAA;cAAMiC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAEhC,OAAO,CAACsC,YAAY,EAAC,QAAM;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxB,KAAK,iBACJd,OAAA;UAAKiC,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFpB;QAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDtC,OAAA;UAAMyC,QAAQ,EAAEnB,YAAa;UAACW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEjDlC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOiC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCN,eAAe,CAACc,GAAG,CAAEC,IAAI,iBACxB3C,OAAA;gBAEEiC,SAAS,EAAE,yEACT3B,cAAc,KAAKqC,IAAI,CAACjB,EAAE,GACtB,4CAA4C,GAC5C,iBAAiB,EACpB;gBAAAQ,QAAA,gBAEHlC,OAAA;kBACE2C,IAAI,EAAC,OAAO;kBACZd,IAAI,EAAC,iBAAiB;kBACtBe,KAAK,EAAED,IAAI,CAACjB,EAAG;kBACfmB,OAAO,EAAEvC,cAAc,KAAKqC,IAAI,CAACjB,EAAG;kBACpCoB,QAAQ,EAAGvB,CAAC,IAAKhB,iBAAiB,CAACgB,CAAC,CAACwB,MAAM,CAACH,KAAY,CAAE;kBAC1DX,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACFtC,OAAA;kBAAKiC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClC,OAAA,CAAC2C,IAAI,CAACZ,IAAI;oBAACE,SAAS,EAAE,gBACpB3B,cAAc,KAAKqC,IAAI,CAACjB,EAAE,GAAG,kBAAkB,GAAG,eAAe;kBAChE;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACNtC,OAAA;oBAAAkC,QAAA,gBACElC,OAAA;sBAAMiC,SAAS,EAAE,uBACf3B,cAAc,KAAKqC,IAAI,CAACjB,EAAE,GAAG,kBAAkB,GAAG,eAAe,EAChE;sBAAAQ,QAAA,EACAS,IAAI,CAACd;oBAAI;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACPtC,OAAA;sBAAGiC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAES,IAAI,CAACb;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLhC,cAAc,KAAKqC,IAAI,CAACjB,EAAE,iBACzB1B,OAAA;kBAAKiC,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACjG;cAAA,GA9BIK,IAAI,CAACjB,EAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+BP,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOiC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAC5D5B,cAAc,KAAK,KAAK,GAAG,oBAAoB,GAC/CA,cAAc,KAAK,KAAK,GAAG,iBAAiB,GAAG;YAAoB;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACRtC,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbK,QAAQ;cACRC,GAAG,EAAC,GAAG;cACPL,KAAK,EAAEpC,eAAgB;cACvBsC,QAAQ,EAAGvB,CAAC,IAAKd,kBAAkB,CAACyC,QAAQ,CAAC3B,CAAC,CAACwB,MAAM,CAACH,KAAK,CAAC,IAAI,CAAC,CAAE;cACnEX,SAAS,EAAC,uHAAuH;cACjIkB,WAAW,EAAC;YAAgB;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtC,OAAA;YAAKiC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxClC,OAAA;cAAIiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEtC,OAAA;cAAKiC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBAAMiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDtC,OAAA;kBAAMiC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GAAEhC,OAAO,CAACe,aAAa,EAAC,QAAM;gBAAA;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBAAMiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjDtC,OAAA;kBAAMiC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GAAEd,WAAW,EAAC,QAAM;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBAAMiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CtC,OAAA;kBAAMiC,SAAS,EAAE,eACfZ,UAAU,GAAG,CAAC,GAAG,gBAAgB,GAAGA,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,eAAe,EACpF;kBAAAa,QAAA,GACAb,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,UAAU,EAAC,QACzC;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLlB,WAAW,IAAIlB,OAAO,CAACsC,YAAY,IAAIpB,WAAW,GAAG,CAAC,iBACrDpB,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClC,OAAA,CAACF,aAAa;gBAACmC,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjEtC,OAAA;gBAAMiC,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GAAC,8CACI,EAAChC,OAAO,CAACsC,YAAY,EAAC,SACpE;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAEAlB,WAAW,KAAK,CAAC,iBAChBpB,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClC,OAAA,CAACF,aAAa;gBAACmC,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DtC,OAAA;gBAAMiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOiC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtC,OAAA;cACE4C,KAAK,EAAElC,MAAO;cACdoC,QAAQ,EAAGvB,CAAC,IAAKZ,SAAS,CAACY,CAAC,CAACwB,MAAM,CAACH,KAAK,CAAE;cAC3CX,SAAS,EAAC,4HAA4H;cAAAC,QAAA,gBAEtIlC,OAAA;gBAAQ4C,KAAK,EAAC,EAAE;gBAAAV,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCN,aAAa,CAACU,GAAG,CAACU,YAAY,iBAC7BpD,OAAA;gBAA2B4C,KAAK,EAAEQ,YAAa;gBAAAlB,QAAA,EAAEkB;cAAY,GAAhDA,YAAY;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6C,CACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR5B,MAAM,KAAK,OAAO,iBACjBV,OAAA;cACEmD,WAAW,EAAC,8BAA8B;cAC1ClB,SAAS,EAAC,uHAAuH;cACjIoB,IAAI,EAAE;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtC,OAAA;YAAKiC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDlC,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbJ,OAAO,EAAEpC,OAAQ;cACjB8B,SAAS,EAAC,uJAAuJ;cAAAC,QAAA,EAClK;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbW,QAAQ,EAAE1C,OAAO,IAAIJ,eAAe,GAAG,CAAC,IAAKF,cAAc,KAAK,UAAU,IAAIE,eAAe,GAAGN,OAAO,CAACe,aAAe;cACvHgB,SAAS,EAAC,gMAAgM;cAAAC,QAAA,EAEzMtB,OAAO,GAAG,cAAc,GAAG;YAAc;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAxQIJ,oBAAyD;AAAAsD,EAAA,GAAzDtD,oBAAyD;AA0Q/D,eAAeA,oBAAoB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}