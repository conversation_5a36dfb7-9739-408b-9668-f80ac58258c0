{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useDashboard } from '../../hooks/useDashboard';\nimport { DollarSign, ShoppingCart, Package, Users, TrendingUp, TrendingDown, AlertTriangle, RefreshCw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const {\n    stats,\n    recentTransactions,\n    lowStockItems,\n    loading,\n    error,\n    refreshData\n  } = useDashboard();\n\n  // Format currency\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-KE', {\n      style: 'currency',\n      currency: 'KES',\n      minimumFractionDigits: 0\n    }).format(amount);\n  };\n\n  // Format change percentage\n  const formatChange = change => {\n    const sign = change >= 0 ? '+' : '';\n    return `${sign}${change}%`;\n  };\n\n  // Get change type and icon\n  const getChangeDisplay = change => {\n    const isPositive = change >= 0;\n    return {\n      type: isPositive ? 'increase' : 'decrease',\n      icon: isPositive ? TrendingUp : TrendingDown,\n      color: isPositive ? 'text-green-600' : 'text-red-600'\n    };\n  };\n\n  // Prepare stats data for display\n  const statsData = [{\n    name: 'Today\\'s Sales',\n    value: formatCurrency(stats.todaysSales),\n    change: formatChange(stats.salesChange),\n    changeDisplay: getChangeDisplay(stats.salesChange),\n    icon: DollarSign\n  }, {\n    name: 'Transactions',\n    value: stats.todaysTransactions.toString(),\n    change: formatChange(stats.transactionsChange),\n    changeDisplay: getChangeDisplay(stats.transactionsChange),\n    icon: ShoppingCart\n  }, {\n    name: 'Low Stock Items',\n    value: stats.lowStockCount.toString(),\n    change: stats.lowStockCount > 0 ? 'Needs attention' : 'All good',\n    changeDisplay: {\n      type: stats.lowStockCount > 0 ? 'decrease' : 'increase',\n      icon: AlertTriangle,\n      color: stats.lowStockCount > 0 ? 'text-orange-600' : 'text-green-600'\n    },\n    icon: Package\n  }, {\n    name: 'Active Customers',\n    value: stats.activeCustomers.toString(),\n    change: 'Today',\n    changeDisplay: {\n      type: 'increase',\n      icon: TrendingUp,\n      color: 'text-blue-600'\n    },\n    icon: Users\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-pulse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 bg-gray-200 rounded w-1/3 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-1/2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n        children: [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white overflow-hidden shadow rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-5\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-pulse\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-6 bg-gray-200 rounded w-1/4 mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            className: \"h-5 w-5 text-red-400 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-800\",\n            children: [\"Error loading dashboard: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refreshData,\n            className: \"ml-auto text-red-600 hover:text-red-800\",\n            children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white overflow-hidden shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"Welcome back, \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.name, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"Here's what's happening with your business today.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refreshData,\n            className: \"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n      children: statsData.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                className: \"h-6 w-6 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: stat.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"flex items-baseline\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-semibold text-gray-900\",\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `ml-2 flex items-baseline text-sm font-semibold ${stat.changeDisplay.color}`,\n                    children: [/*#__PURE__*/_jsxDEV(stat.changeDisplay.icon, {\n                      className: \"self-center flex-shrink-0 h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-1\",\n                      children: stat.change\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)\n      }, stat.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-gray-900 mb-4\",\n            children: \"Recent Transactions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: recentTransactions.length > 0 ? recentTransactions.map(transaction => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: transaction.customerName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [transaction.type, \" \\u2022 \", transaction.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-semibold text-gray-900\",\n                children: formatCurrency(transaction.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this)]\n            }, transaction.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"No recent transactions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-sm text-primary-600 hover:text-primary-500 font-medium\",\n              children: \"View all transactions \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-5 w-5 text-orange-500 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg leading-6 font-medium text-gray-900\",\n              children: \"Low Stock Alerts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: lowStockItems.length > 0 ? lowStockItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [\"Current: \", item.current, \" | Minimum: \", item.minimum]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-semibold text-orange-600\",\n                children: \"Reorder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-green-600\",\n                children: \"All products are well stocked!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-sm text-primary-600 hover:text-primary-500 font-medium\",\n              children: \"Manage inventory \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"BHsHBYSY8rgCX4d+hqDrruFRlYM=\", false, function () {\n  return [useAuth, useDashboard];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useAuth", "useDashboard", "DollarSign", "ShoppingCart", "Package", "Users", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "currentUser", "stats", "recentTransactions", "lowStockItems", "loading", "error", "refreshData", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "formatChange", "change", "sign", "getChangeDisplay", "isPositive", "type", "icon", "color", "statsData", "name", "value", "todaysSales", "salesChange", "changeDisplay", "todaysTransactions", "toString", "transactionsChange", "lowStockCount", "activeCustomers", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "i", "onClick", "stat", "length", "transaction", "customerName", "time", "id", "item", "current", "minimum", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/dashboard/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useDashboard } from '../../hooks/useDashboard';\nimport {\n  DollarSign,\n  ShoppingCart,\n  Package,\n  Users,\n  TrendingUp,\n  TrendingDown,\n  AlertTriangle,\n  RefreshCw\n} from 'lucide-react';\n\nconst Dashboard: React.FC = () => {\n  const { currentUser } = useAuth();\n  const {\n    stats,\n    recentTransactions,\n    lowStockItems,\n    loading,\n    error,\n    refreshData\n  } = useDashboard();\n\n  // Format currency\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-KE', {\n      style: 'currency',\n      currency: 'KES',\n      minimumFractionDigits: 0,\n    }).format(amount);\n  };\n\n  // Format change percentage\n  const formatChange = (change: number) => {\n    const sign = change >= 0 ? '+' : '';\n    return `${sign}${change}%`;\n  };\n\n  // Get change type and icon\n  const getChangeDisplay = (change: number) => {\n    const isPositive = change >= 0;\n    return {\n      type: isPositive ? 'increase' : 'decrease',\n      icon: isPositive ? TrendingUp : TrendingDown,\n      color: isPositive ? 'text-green-600' : 'text-red-600',\n    };\n  };\n\n  // Prepare stats data for display\n  const statsData = [\n    {\n      name: 'Today\\'s Sales',\n      value: formatCurrency(stats.todaysSales),\n      change: formatChange(stats.salesChange),\n      changeDisplay: getChangeDisplay(stats.salesChange),\n      icon: DollarSign,\n    },\n    {\n      name: 'Transactions',\n      value: stats.todaysTransactions.toString(),\n      change: formatChange(stats.transactionsChange),\n      changeDisplay: getChangeDisplay(stats.transactionsChange),\n      icon: ShoppingCart,\n    },\n    {\n      name: 'Low Stock Items',\n      value: stats.lowStockCount.toString(),\n      change: stats.lowStockCount > 0 ? 'Needs attention' : 'All good',\n      changeDisplay: {\n        type: stats.lowStockCount > 0 ? 'decrease' : 'increase',\n        icon: AlertTriangle,\n        color: stats.lowStockCount > 0 ? 'text-orange-600' : 'text-green-600',\n      },\n      icon: Package,\n    },\n    {\n      name: 'Active Customers',\n      value: stats.activeCustomers.toString(),\n      change: 'Today',\n      changeDisplay: {\n        type: 'increase',\n        icon: TrendingUp,\n        color: 'text-blue-600',\n      },\n      icon: Users,\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-200 rounded w-1/3 mb-2\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n          {[1, 2, 3, 4].map((i) => (\n            <div key={i} className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"animate-pulse\">\n                  <div className=\"h-6 bg-gray-200 rounded w-1/4 mb-2\"></div>\n                  <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertTriangle className=\"h-5 w-5 text-red-400 mr-2\" />\n            <p className=\"text-red-800\">Error loading dashboard: {error}</p>\n            <button\n              onClick={refreshData}\n              className=\"ml-auto text-red-600 hover:text-red-800\"\n            >\n              <RefreshCw className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Welcome back, {currentUser?.name}!\n              </h1>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Here's what's happening with your business today.\n              </p>\n            </div>\n            <button\n              onClick={refreshData}\n              className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n            >\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Refresh\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        {statsData.map((stat) => (\n          <div key={stat.name} className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <stat.icon className=\"h-6 w-6 text-gray-400\" />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      {stat.name}\n                    </dt>\n                    <dd className=\"flex items-baseline\">\n                      <div className=\"text-2xl font-semibold text-gray-900\">\n                        {stat.value}\n                      </div>\n                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${stat.changeDisplay.color}`}>\n                        <stat.changeDisplay.icon className=\"self-center flex-shrink-0 h-4 w-4\" />\n                        <span className=\"ml-1\">{stat.change}</span>\n                      </div>\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Transactions */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              Recent Transactions\n            </h3>\n            <div className=\"space-y-3\">\n              {recentTransactions.length > 0 ? (\n                recentTransactions.map((transaction) => (\n                  <div key={transaction.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{transaction.customerName}</p>\n                      <p className=\"text-sm text-gray-500\">{transaction.type} • {transaction.time}</p>\n                    </div>\n                    <div className=\"text-sm font-semibold text-gray-900\">\n                      {formatCurrency(transaction.amount)}\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"text-center py-4\">\n                  <p className=\"text-sm text-gray-500\">No recent transactions</p>\n                </div>\n              )}\n            </div>\n            <div className=\"mt-4\">\n              <button className=\"text-sm text-primary-600 hover:text-primary-500 font-medium\">\n                View all transactions →\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Low Stock Alerts */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center mb-4\">\n              <AlertTriangle className=\"h-5 w-5 text-orange-500 mr-2\" />\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                Low Stock Alerts\n              </h3>\n            </div>\n            <div className=\"space-y-3\">\n              {lowStockItems.length > 0 ? (\n                lowStockItems.map((item) => (\n                  <div key={item.id} className=\"flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{item.name}</p>\n                      <p className=\"text-sm text-gray-500\">\n                        Current: {item.current} | Minimum: {item.minimum}\n                      </p>\n                    </div>\n                    <div className=\"text-sm font-semibold text-orange-600\">\n                      Reorder\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"text-center py-4\">\n                  <p className=\"text-sm text-green-600\">All products are well stocked!</p>\n                </div>\n              )}\n            </div>\n            <div className=\"mt-4\">\n              <button className=\"text-sm text-primary-600 hover:text-primary-500 font-medium\">\n                Manage inventory →\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SACEC,UAAU,EACVC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,aAAa,EACbC,SAAS,QACJ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAY,CAAC,GAAGd,OAAO,CAAC,CAAC;EACjC,MAAM;IACJe,KAAK;IACLC,kBAAkB;IAClBC,aAAa;IACbC,OAAO;IACPC,KAAK;IACLC;EACF,CAAC,GAAGnB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMoB,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,MAAMO,YAAY,GAAIC,MAAc,IAAK;IACvC,MAAMC,IAAI,GAAGD,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IACnC,OAAO,GAAGC,IAAI,GAAGD,MAAM,GAAG;EAC5B,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIF,MAAc,IAAK;IAC3C,MAAMG,UAAU,GAAGH,MAAM,IAAI,CAAC;IAC9B,OAAO;MACLI,IAAI,EAAED,UAAU,GAAG,UAAU,GAAG,UAAU;MAC1CE,IAAI,EAAEF,UAAU,GAAG3B,UAAU,GAAGC,YAAY;MAC5C6B,KAAK,EAAEH,UAAU,GAAG,gBAAgB,GAAG;IACzC,CAAC;EACH,CAAC;;EAED;EACA,MAAMI,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAElB,cAAc,CAACN,KAAK,CAACyB,WAAW,CAAC;IACxCV,MAAM,EAAED,YAAY,CAACd,KAAK,CAAC0B,WAAW,CAAC;IACvCC,aAAa,EAAEV,gBAAgB,CAACjB,KAAK,CAAC0B,WAAW,CAAC;IAClDN,IAAI,EAAEjC;EACR,CAAC,EACD;IACEoC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAExB,KAAK,CAAC4B,kBAAkB,CAACC,QAAQ,CAAC,CAAC;IAC1Cd,MAAM,EAAED,YAAY,CAACd,KAAK,CAAC8B,kBAAkB,CAAC;IAC9CH,aAAa,EAAEV,gBAAgB,CAACjB,KAAK,CAAC8B,kBAAkB,CAAC;IACzDV,IAAI,EAAEhC;EACR,CAAC,EACD;IACEmC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAExB,KAAK,CAAC+B,aAAa,CAACF,QAAQ,CAAC,CAAC;IACrCd,MAAM,EAAEf,KAAK,CAAC+B,aAAa,GAAG,CAAC,GAAG,iBAAiB,GAAG,UAAU;IAChEJ,aAAa,EAAE;MACbR,IAAI,EAAEnB,KAAK,CAAC+B,aAAa,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;MACvDX,IAAI,EAAE3B,aAAa;MACnB4B,KAAK,EAAErB,KAAK,CAAC+B,aAAa,GAAG,CAAC,GAAG,iBAAiB,GAAG;IACvD,CAAC;IACDX,IAAI,EAAE/B;EACR,CAAC,EACD;IACEkC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAExB,KAAK,CAACgC,eAAe,CAACH,QAAQ,CAAC,CAAC;IACvCd,MAAM,EAAE,OAAO;IACfY,aAAa,EAAE;MACbR,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE7B,UAAU;MAChB8B,KAAK,EAAE;IACT,CAAC;IACDD,IAAI,EAAE9B;EACR,CAAC,CACF;EAED,IAAIa,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKqC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBtC,OAAA;QAAKqC,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDtC,OAAA;UAAKqC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BtC,OAAA;YAAKqC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtC,OAAA;cAAKqC,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D1C,OAAA;cAAKqC,SAAS,EAAC;YAA+B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1C,OAAA;QAAKqC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACK,GAAG,CAAEC,CAAC,iBAClB5C,OAAA;UAAaqC,SAAS,EAAC,4CAA4C;UAAAC,QAAA,eACjEtC,OAAA;YAAKqC,SAAS,EAAC,KAAK;YAAAC,QAAA,eAClBtC,OAAA;cAAKqC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtC,OAAA;gBAAKqC,SAAS,EAAC;cAAoC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D1C,OAAA;gBAAKqC,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GANEE,CAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAON,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIlC,KAAK,EAAE;IACT,oBACER,OAAA;MAAKqC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBtC,OAAA;QAAKqC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7DtC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtC,OAAA,CAACH,aAAa;YAACwC,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvD1C,OAAA;YAAGqC,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAC,2BAAyB,EAAC9B,KAAK;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE1C,OAAA;YACE6C,OAAO,EAAEpC,WAAY;YACrB4B,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eAEnDtC,OAAA,CAACF,SAAS;cAACuC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtC,OAAA;MAAKqC,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzDtC,OAAA;QAAKqC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BtC,OAAA;UAAKqC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDtC,OAAA;YAAAsC,QAAA,gBACEtC,OAAA;cAAIqC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,gBACjC,EAACnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,IAAI,EAAC,GACnC;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1C,OAAA;cAAGqC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1C,OAAA;YACE6C,OAAO,EAAEpC,WAAY;YACrB4B,SAAS,EAAC,iOAAiO;YAAAC,QAAA,gBAE3OtC,OAAA,CAACF,SAAS;cAACuC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKqC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEZ,SAAS,CAACiB,GAAG,CAAEG,IAAI,iBAClB9C,OAAA;QAAqBqC,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzEtC,OAAA;UAAKqC,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBtC,OAAA;YAAKqC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtC,OAAA;cAAKqC,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BtC,OAAA,CAAC8C,IAAI,CAACtB,IAAI;gBAACa,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BtC,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAIqC,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACvDQ,IAAI,CAACnB;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACL1C,OAAA;kBAAIqC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjCtC,OAAA;oBAAKqC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAClDQ,IAAI,CAAClB;kBAAK;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACN1C,OAAA;oBAAKqC,SAAS,EAAE,kDAAkDS,IAAI,CAACf,aAAa,CAACN,KAAK,EAAG;oBAAAa,QAAA,gBAC3FtC,OAAA,CAAC8C,IAAI,CAACf,aAAa,CAACP,IAAI;sBAACa,SAAS,EAAC;oBAAmC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzE1C,OAAA;sBAAMqC,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEQ,IAAI,CAAC3B;oBAAM;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAvBEI,IAAI,CAACnB,IAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN1C,OAAA;MAAKqC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDtC,OAAA;QAAKqC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCtC,OAAA;UAAKqC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtC,OAAA;YAAIqC,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1C,OAAA;YAAKqC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBjC,kBAAkB,CAAC0C,MAAM,GAAG,CAAC,GAC5B1C,kBAAkB,CAACsC,GAAG,CAAEK,WAAW,iBACjChD,OAAA;cAA0BqC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC/FtC,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAGqC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEU,WAAW,CAACC;gBAAY;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/E1C,OAAA;kBAAGqC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAEU,WAAW,CAACzB,IAAI,EAAC,UAAG,EAACyB,WAAW,CAACE,IAAI;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EACjD5B,cAAc,CAACsC,WAAW,CAACrC,MAAM;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA,GAPEM,WAAW,CAACG,EAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQnB,CACN,CAAC,gBAEF1C,OAAA;cAAKqC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BtC,OAAA;gBAAGqC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBtC,OAAA;cAAQqC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAC;YAEhF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCtC,OAAA;UAAKqC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtC,OAAA;YAAKqC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCtC,OAAA,CAACH,aAAa;cAACwC,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D1C,OAAA;cAAIqC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE5D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhC,aAAa,CAACyC,MAAM,GAAG,CAAC,GACvBzC,aAAa,CAACqC,GAAG,CAAES,IAAI,iBACrBpD,OAAA;cAAmBqC,SAAS,EAAC,wFAAwF;cAAAC,QAAA,gBACnHtC,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAGqC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEc,IAAI,CAACzB;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChE1C,OAAA;kBAAGqC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,WAC1B,EAACc,IAAI,CAACC,OAAO,EAAC,cAAY,EAACD,IAAI,CAACE,OAAO;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAEvD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GATEU,IAAI,CAACD,EAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUZ,CACN,CAAC,gBAEF1C,OAAA;cAAKqC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BtC,OAAA;gBAAGqC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBtC,OAAA;cAAQqC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAC;YAEhF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CA5PID,SAAmB;EAAA,QACCZ,OAAO,EAQ3BC,YAAY;AAAA;AAAAiE,EAAA,GATZtD,SAAmB;AA8PzB,eAAeA,SAAS;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}