{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\offline\\\\OfflineManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Wifi, WifiOff, RefreshCw, Clock, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OfflineManager = () => {\n  _s();\n  const [isOnline, setIsOnline] = useState(navigator.onLine);\n  const [offlineQueue, setOfflineQueue] = useState([]);\n  const [showOfflineIndicator, setShowOfflineIndicator] = useState(false);\n  const [syncInProgress, setSyncInProgress] = useState(false);\n  const [lastSyncTime, setLastSyncTime] = useState(null);\n\n  // Monitor online/offline status\n  useEffect(() => {\n    const handleOnline = () => {\n      setIsOnline(true);\n      setShowOfflineIndicator(false);\n      // Trigger sync when coming back online\n      if (offlineQueue.length > 0) {\n        syncOfflineData();\n      }\n    };\n    const handleOffline = () => {\n      setIsOnline(false);\n      setShowOfflineIndicator(true);\n    };\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, [offlineQueue.length]);\n\n  // Load offline queue from localStorage\n  useEffect(() => {\n    const loadOfflineQueue = () => {\n      try {\n        const stored = localStorage.getItem('offline-queue');\n        if (stored) {\n          const queue = JSON.parse(stored);\n          setOfflineQueue(queue);\n        }\n      } catch (error) {\n        console.error('Error loading offline queue:', error);\n      }\n    };\n    loadOfflineQueue();\n\n    // Listen for storage changes (from service worker)\n    const handleStorageChange = e => {\n      if (e.key === 'offline-queue') {\n        loadOfflineQueue();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  // Listen for service worker messages\n  useEffect(() => {\n    var _navigator$serviceWor;\n    const handleServiceWorkerMessage = event => {\n      var _event$data;\n      if (((_event$data = event.data) === null || _event$data === void 0 ? void 0 : _event$data.type) === 'OFFLINE_SYNC_COMPLETE') {\n        setSyncInProgress(false);\n        setLastSyncTime(new Date());\n\n        // Reload offline queue\n        const stored = localStorage.getItem('offline-queue');\n        if (stored) {\n          setOfflineQueue(JSON.parse(stored));\n        } else {\n          setOfflineQueue([]);\n        }\n\n        // Show success notification\n        showNotification(`Sync completed! ${event.data.processedCount} items synchronized.`, 'success');\n      }\n    };\n    (_navigator$serviceWor = navigator.serviceWorker) === null || _navigator$serviceWor === void 0 ? void 0 : _navigator$serviceWor.addEventListener('message', handleServiceWorkerMessage);\n    return () => {\n      var _navigator$serviceWor2;\n      (_navigator$serviceWor2 = navigator.serviceWorker) === null || _navigator$serviceWor2 === void 0 ? void 0 : _navigator$serviceWor2.removeEventListener('message', handleServiceWorkerMessage);\n    };\n  }, []);\n\n  // Manually trigger sync\n  const syncOfflineData = async () => {\n    if (!isOnline || syncInProgress) return;\n    setSyncInProgress(true);\n    try {\n      var _navigator$serviceWor3;\n      // Send message to service worker to process queue\n      if ((_navigator$serviceWor3 = navigator.serviceWorker) !== null && _navigator$serviceWor3 !== void 0 && _navigator$serviceWor3.controller) {\n        navigator.serviceWorker.controller.postMessage({\n          type: 'PROCESS_OFFLINE_QUEUE'\n        });\n      }\n    } catch (error) {\n      console.error('Error triggering sync:', error);\n      setSyncInProgress(false);\n      showNotification('Sync failed. Please try again.', 'error');\n    }\n  };\n\n  // Clear offline queue\n  const clearOfflineQueue = () => {\n    localStorage.removeItem('offline-queue');\n    setOfflineQueue([]);\n    showNotification('Offline queue cleared.', 'success');\n  };\n\n  // Show notification\n  const showNotification = (message, type) => {\n    // This would integrate with your notification system\n    console.log(`${type.toUpperCase()}: ${message}`);\n  };\n\n  // Format timestamp\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleString();\n  };\n\n  // Get description for queued item\n  const getItemDescription = item => {\n    if (item.description) return item.description;\n\n    // Generate description based on URL and method\n    const url = new URL(item.url);\n    const path = url.pathname;\n    if (path.includes('/transactions')) {\n      return item.method === 'POST' ? 'New transaction' : 'Transaction update';\n    } else if (path.includes('/products')) {\n      return item.method === 'POST' ? 'New product' : 'Product update';\n    } else if (path.includes('/services')) {\n      return item.method === 'POST' ? 'New service' : 'Service update';\n    }\n    return `${item.method} ${path}`;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [showOfflineIndicator && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 bg-orange-500 text-white px-4 py-2 text-center text-sm font-medium z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(WifiOff, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), \"You are currently offline. Changes will be synced when connection is restored.\", offlineQueue.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-2 bg-orange-600 px-2 py-1 rounded text-xs\",\n          children: [offlineQueue.length, \" pending\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex items-center px-3 py-2 rounded-lg shadow-lg text-sm font-medium ${isOnline ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'}`,\n        children: [isOnline ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Wifi, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), \"Online\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(WifiOff, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), \"Offline\"]\n        }, void 0, true), offlineQueue.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-2 bg-gray-600 text-white px-2 py-1 rounded text-xs\",\n          children: offlineQueue.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), offlineQueue.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-20 right-4 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-40\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-semibold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), \"Pending Sync (\", offlineQueue.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [isOnline && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: syncOfflineData,\n              disabled: syncInProgress,\n              className: \"text-blue-600 hover:text-blue-800 disabled:opacity-50\",\n              title: \"Sync now\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: `h-4 w-4 ${syncInProgress ? 'animate-spin' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearOfflineQueue,\n              className: \"text-red-600 hover:text-red-800\",\n              title: \"Clear queue\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), lastSyncTime && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 mt-1\",\n          children: [\"Last sync: \", lastSyncTime.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-h-64 overflow-y-auto\",\n        children: offlineQueue.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 border-b border-gray-100 last:border-b-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: getItemDescription(item)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: formatTimestamp(item.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center ml-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${item.method === 'POST' ? 'bg-green-100 text-green-800' : item.method === 'PUT' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'}`,\n                children: item.method\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), isOnline && offlineQueue.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 bg-gray-50 rounded-b-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: syncOfflineData,\n          disabled: syncInProgress,\n          className: \"w-full bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center\",\n          children: syncInProgress ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: \"h-4 w-4 mr-2 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 21\n            }, this), \"Syncing...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 21\n            }, this), \"Sync All\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this), syncInProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 left-4 bg-blue-100 border border-blue-200 text-blue-800 px-4 py-2 rounded-lg shadow-lg z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          className: \"h-4 w-4 mr-2 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: \"Syncing offline data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(OfflineManager, \"otqoLy3qZ5a9TIAnVYS4lC6ohSw=\");\n_c = OfflineManager;\nexport default OfflineManager;\nvar _c;\n$RefreshReg$(_c, \"OfflineManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "Clock", "X", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OfflineManager", "_s", "isOnline", "setIsOnline", "navigator", "onLine", "offlineQueue", "setOfflineQueue", "showOfflineIndicator", "setShowOfflineIndicator", "syncInProgress", "setSyncInProgress", "lastSyncTime", "setLastSyncTime", "handleOnline", "length", "syncOfflineData", "handleOffline", "window", "addEventListener", "removeEventListener", "loadOfflineQueue", "stored", "localStorage", "getItem", "queue", "JSON", "parse", "error", "console", "handleStorageChange", "e", "key", "_navigator$serviceWor", "handleServiceWorkerMessage", "event", "_event$data", "data", "type", "Date", "showNotification", "processedCount", "serviceWorker", "_navigator$serviceWor2", "_navigator$serviceWor3", "controller", "postMessage", "clearOfflineQueue", "removeItem", "message", "log", "toUpperCase", "formatTimestamp", "timestamp", "toLocaleString", "getItemDescription", "item", "description", "url", "URL", "path", "pathname", "includes", "method", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "toLocaleTimeString", "map", "index", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/offline/OfflineManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Wifi,\n  WifiOff,\n  Refresh<PERSON>w,\n  AlertCircle,\n  CheckCircle,\n  Clock,\n  X\n} from 'lucide-react';\n\ninterface OfflineQueueItem {\n  url: string;\n  method: string;\n  headers: Record<string, string>;\n  body: string | null;\n  timestamp: number;\n  description?: string;\n}\n\nconst OfflineManager: React.FC = () => {\n  const [isOnline, setIsOnline] = useState(navigator.onLine);\n  const [offlineQueue, setOfflineQueue] = useState<OfflineQueueItem[]>([]);\n  const [showOfflineIndicator, setShowOfflineIndicator] = useState(false);\n  const [syncInProgress, setSyncInProgress] = useState(false);\n  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);\n\n  // Monitor online/offline status\n  useEffect(() => {\n    const handleOnline = () => {\n      setIsOnline(true);\n      setShowOfflineIndicator(false);\n      // Trigger sync when coming back online\n      if (offlineQueue.length > 0) {\n        syncOfflineData();\n      }\n    };\n\n    const handleOffline = () => {\n      setIsOnline(false);\n      setShowOfflineIndicator(true);\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, [offlineQueue.length]);\n\n  // Load offline queue from localStorage\n  useEffect(() => {\n    const loadOfflineQueue = () => {\n      try {\n        const stored = localStorage.getItem('offline-queue');\n        if (stored) {\n          const queue = JSON.parse(stored);\n          setOfflineQueue(queue);\n        }\n      } catch (error) {\n        console.error('Error loading offline queue:', error);\n      }\n    };\n\n    loadOfflineQueue();\n\n    // Listen for storage changes (from service worker)\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'offline-queue') {\n        loadOfflineQueue();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  // Listen for service worker messages\n  useEffect(() => {\n    const handleServiceWorkerMessage = (event: MessageEvent) => {\n      if (event.data?.type === 'OFFLINE_SYNC_COMPLETE') {\n        setSyncInProgress(false);\n        setLastSyncTime(new Date());\n        \n        // Reload offline queue\n        const stored = localStorage.getItem('offline-queue');\n        if (stored) {\n          setOfflineQueue(JSON.parse(stored));\n        } else {\n          setOfflineQueue([]);\n        }\n\n        // Show success notification\n        showNotification(\n          `Sync completed! ${event.data.processedCount} items synchronized.`,\n          'success'\n        );\n      }\n    };\n\n    navigator.serviceWorker?.addEventListener('message', handleServiceWorkerMessage);\n    return () => {\n      navigator.serviceWorker?.removeEventListener('message', handleServiceWorkerMessage);\n    };\n  }, []);\n\n  // Manually trigger sync\n  const syncOfflineData = async () => {\n    if (!isOnline || syncInProgress) return;\n\n    setSyncInProgress(true);\n    \n    try {\n      // Send message to service worker to process queue\n      if (navigator.serviceWorker?.controller) {\n        navigator.serviceWorker.controller.postMessage({\n          type: 'PROCESS_OFFLINE_QUEUE'\n        });\n      }\n    } catch (error) {\n      console.error('Error triggering sync:', error);\n      setSyncInProgress(false);\n      showNotification('Sync failed. Please try again.', 'error');\n    }\n  };\n\n  // Clear offline queue\n  const clearOfflineQueue = () => {\n    localStorage.removeItem('offline-queue');\n    setOfflineQueue([]);\n    showNotification('Offline queue cleared.', 'success');\n  };\n\n  // Show notification\n  const showNotification = (message: string, type: 'success' | 'error' | 'info') => {\n    // This would integrate with your notification system\n    console.log(`${type.toUpperCase()}: ${message}`);\n  };\n\n  // Format timestamp\n  const formatTimestamp = (timestamp: number) => {\n    return new Date(timestamp).toLocaleString();\n  };\n\n  // Get description for queued item\n  const getItemDescription = (item: OfflineQueueItem) => {\n    if (item.description) return item.description;\n    \n    // Generate description based on URL and method\n    const url = new URL(item.url);\n    const path = url.pathname;\n    \n    if (path.includes('/transactions')) {\n      return item.method === 'POST' ? 'New transaction' : 'Transaction update';\n    } else if (path.includes('/products')) {\n      return item.method === 'POST' ? 'New product' : 'Product update';\n    } else if (path.includes('/services')) {\n      return item.method === 'POST' ? 'New service' : 'Service update';\n    }\n    \n    return `${item.method} ${path}`;\n  };\n\n  return (\n    <>\n      {/* Offline Indicator */}\n      {showOfflineIndicator && (\n        <div className=\"fixed top-0 left-0 right-0 bg-orange-500 text-white px-4 py-2 text-center text-sm font-medium z-50\">\n          <div className=\"flex items-center justify-center\">\n            <WifiOff className=\"h-4 w-4 mr-2\" />\n            You are currently offline. Changes will be synced when connection is restored.\n            {offlineQueue.length > 0 && (\n              <span className=\"ml-2 bg-orange-600 px-2 py-1 rounded text-xs\">\n                {offlineQueue.length} pending\n              </span>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Connection Status in Corner */}\n      <div className=\"fixed bottom-4 right-4 z-40\">\n        <div className={`flex items-center px-3 py-2 rounded-lg shadow-lg text-sm font-medium ${\n          isOnline \n            ? 'bg-green-100 text-green-800 border border-green-200' \n            : 'bg-red-100 text-red-800 border border-red-200'\n        }`}>\n          {isOnline ? (\n            <>\n              <Wifi className=\"h-4 w-4 mr-2\" />\n              Online\n            </>\n          ) : (\n            <>\n              <WifiOff className=\"h-4 w-4 mr-2\" />\n              Offline\n            </>\n          )}\n          \n          {offlineQueue.length > 0 && (\n            <span className=\"ml-2 bg-gray-600 text-white px-2 py-1 rounded text-xs\">\n              {offlineQueue.length}\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Offline Queue Panel */}\n      {offlineQueue.length > 0 && (\n        <div className=\"fixed bottom-20 right-4 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-40\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-sm font-semibold text-gray-900 flex items-center\">\n                <Clock className=\"h-4 w-4 mr-2\" />\n                Pending Sync ({offlineQueue.length})\n              </h3>\n              <div className=\"flex space-x-2\">\n                {isOnline && (\n                  <button\n                    onClick={syncOfflineData}\n                    disabled={syncInProgress}\n                    className=\"text-blue-600 hover:text-blue-800 disabled:opacity-50\"\n                    title=\"Sync now\"\n                  >\n                    <RefreshCw className={`h-4 w-4 ${syncInProgress ? 'animate-spin' : ''}`} />\n                  </button>\n                )}\n                <button\n                  onClick={clearOfflineQueue}\n                  className=\"text-red-600 hover:text-red-800\"\n                  title=\"Clear queue\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n            \n            {lastSyncTime && (\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Last sync: {lastSyncTime.toLocaleTimeString()}\n              </p>\n            )}\n          </div>\n          \n          <div className=\"max-h-64 overflow-y-auto\">\n            {offlineQueue.map((item, index) => (\n              <div key={index} className=\"p-3 border-b border-gray-100 last:border-b-0\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      {getItemDescription(item)}\n                    </p>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {formatTimestamp(item.timestamp)}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center ml-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      item.method === 'POST' \n                        ? 'bg-green-100 text-green-800'\n                        : item.method === 'PUT'\n                        ? 'bg-blue-100 text-blue-800'\n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {item.method}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          \n          {isOnline && offlineQueue.length > 0 && (\n            <div className=\"p-3 bg-gray-50 rounded-b-lg\">\n              <button\n                onClick={syncOfflineData}\n                disabled={syncInProgress}\n                className=\"w-full bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center\"\n              >\n                {syncInProgress ? (\n                  <>\n                    <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                    Syncing...\n                  </>\n                ) : (\n                  <>\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Sync All\n                  </>\n                )}\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Sync Status Notifications */}\n      {syncInProgress && (\n        <div className=\"fixed bottom-4 left-4 bg-blue-100 border border-blue-200 text-blue-800 px-4 py-2 rounded-lg shadow-lg z-40\">\n          <div className=\"flex items-center\">\n            <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n            <span className=\"text-sm font-medium\">Syncing offline data...</span>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default OfflineManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,OAAO,EACPC,SAAS,EAGTC,KAAK,EACLC,CAAC,QACI,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAWtB,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAACe,SAAS,CAACC,MAAM,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAqB,EAAE,CAAC;EACxE,MAAM,CAACmB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAc,IAAI,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,YAAY,GAAGA,CAAA,KAAM;MACzBX,WAAW,CAAC,IAAI,CAAC;MACjBM,uBAAuB,CAAC,KAAK,CAAC;MAC9B;MACA,IAAIH,YAAY,CAACS,MAAM,GAAG,CAAC,EAAE;QAC3BC,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1Bd,WAAW,CAAC,KAAK,CAAC;MAClBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B,CAAC;IAEDS,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAC/CI,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEF,aAAa,CAAC;IAEjD,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;MAClDI,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACX,YAAY,CAACS,MAAM,CAAC,CAAC;;EAEzB;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM+B,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAI;QACF,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;QACpD,IAAIF,MAAM,EAAE;UACV,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;UAChCf,eAAe,CAACkB,KAAK,CAAC;QACxB;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDP,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMS,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACC,GAAG,KAAK,eAAe,EAAE;QAC7BX,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC;IAEDH,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEW,mBAAmB,CAAC;IACvD,OAAO,MAAMZ,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEU,mBAAmB,CAAC;EACzE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxC,SAAS,CAAC,MAAM;IAAA,IAAA2C,qBAAA;IACd,MAAMC,0BAA0B,GAAIC,KAAmB,IAAK;MAAA,IAAAC,WAAA;MAC1D,IAAI,EAAAA,WAAA,GAAAD,KAAK,CAACE,IAAI,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,IAAI,MAAK,uBAAuB,EAAE;QAChD3B,iBAAiB,CAAC,KAAK,CAAC;QACxBE,eAAe,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;;QAE3B;QACA,MAAMjB,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;QACpD,IAAIF,MAAM,EAAE;UACVf,eAAe,CAACmB,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC,CAAC;QACrC,CAAC,MAAM;UACLf,eAAe,CAAC,EAAE,CAAC;QACrB;;QAEA;QACAiC,gBAAgB,CACd,mBAAmBL,KAAK,CAACE,IAAI,CAACI,cAAc,sBAAsB,EAClE,SACF,CAAC;MACH;IACF,CAAC;IAED,CAAAR,qBAAA,GAAA7B,SAAS,CAACsC,aAAa,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBd,gBAAgB,CAAC,SAAS,EAAEe,0BAA0B,CAAC;IAChF,OAAO,MAAM;MAAA,IAAAS,sBAAA;MACX,CAAAA,sBAAA,GAAAvC,SAAS,CAACsC,aAAa,cAAAC,sBAAA,uBAAvBA,sBAAA,CAAyBvB,mBAAmB,CAAC,SAAS,EAAEc,0BAA0B,CAAC;IACrF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMlB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACd,QAAQ,IAAIQ,cAAc,EAAE;IAEjCC,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MAAA,IAAAiC,sBAAA;MACF;MACA,KAAAA,sBAAA,GAAIxC,SAAS,CAACsC,aAAa,cAAAE,sBAAA,eAAvBA,sBAAA,CAAyBC,UAAU,EAAE;QACvCzC,SAAS,CAACsC,aAAa,CAACG,UAAU,CAACC,WAAW,CAAC;UAC7CR,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CjB,iBAAiB,CAAC,KAAK,CAAC;MACxB6B,gBAAgB,CAAC,gCAAgC,EAAE,OAAO,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxB,YAAY,CAACyB,UAAU,CAAC,eAAe,CAAC;IACxCzC,eAAe,CAAC,EAAE,CAAC;IACnBiC,gBAAgB,CAAC,wBAAwB,EAAE,SAAS,CAAC;EACvD,CAAC;;EAED;EACA,MAAMA,gBAAgB,GAAGA,CAACS,OAAe,EAAEX,IAAkC,KAAK;IAChF;IACAT,OAAO,CAACqB,GAAG,CAAC,GAAGZ,IAAI,CAACa,WAAW,CAAC,CAAC,KAAKF,OAAO,EAAE,CAAC;EAClD,CAAC;;EAED;EACA,MAAMG,eAAe,GAAIC,SAAiB,IAAK;IAC7C,OAAO,IAAId,IAAI,CAACc,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,IAAsB,IAAK;IACrD,IAAIA,IAAI,CAACC,WAAW,EAAE,OAAOD,IAAI,CAACC,WAAW;;IAE7C;IACA,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACH,IAAI,CAACE,GAAG,CAAC;IAC7B,MAAME,IAAI,GAAGF,GAAG,CAACG,QAAQ;IAEzB,IAAID,IAAI,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAE;MAClC,OAAON,IAAI,CAACO,MAAM,KAAK,MAAM,GAAG,iBAAiB,GAAG,oBAAoB;IAC1E,CAAC,MAAM,IAAIH,IAAI,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;MACrC,OAAON,IAAI,CAACO,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,gBAAgB;IAClE,CAAC,MAAM,IAAIH,IAAI,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;MACrC,OAAON,IAAI,CAACO,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,gBAAgB;IAClE;IAEA,OAAO,GAAGP,IAAI,CAACO,MAAM,IAAIH,IAAI,EAAE;EACjC,CAAC;EAED,oBACE/D,OAAA,CAAAE,SAAA;IAAAiE,QAAA,GAEGxD,oBAAoB,iBACnBX,OAAA;MAAKoE,SAAS,EAAC,oGAAoG;MAAAD,QAAA,eACjHnE,OAAA;QAAKoE,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/CnE,OAAA,CAACL,OAAO;UAACyE,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kFAEpC,EAAC/D,YAAY,CAACS,MAAM,GAAG,CAAC,iBACtBlB,OAAA;UAAMoE,SAAS,EAAC,8CAA8C;UAAAD,QAAA,GAC3D1D,YAAY,CAACS,MAAM,EAAC,UACvB;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDxE,OAAA;MAAKoE,SAAS,EAAC,6BAA6B;MAAAD,QAAA,eAC1CnE,OAAA;QAAKoE,SAAS,EAAE,wEACd/D,QAAQ,GACJ,qDAAqD,GACrD,+CAA+C,EAClD;QAAA8D,QAAA,GACA9D,QAAQ,gBACPL,OAAA,CAAAE,SAAA;UAAAiE,QAAA,gBACEnE,OAAA,CAACN,IAAI;YAAC0E,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEnC;QAAA,eAAE,CAAC,gBAEHxE,OAAA,CAAAE,SAAA;UAAAiE,QAAA,gBACEnE,OAAA,CAACL,OAAO;YAACyE,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAEtC;QAAA,eAAE,CACH,EAEA/D,YAAY,CAACS,MAAM,GAAG,CAAC,iBACtBlB,OAAA;UAAMoE,SAAS,EAAC,uDAAuD;UAAAD,QAAA,EACpE1D,YAAY,CAACS;QAAM;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/D,YAAY,CAACS,MAAM,GAAG,CAAC,iBACtBlB,OAAA;MAAKoE,SAAS,EAAC,wFAAwF;MAAAD,QAAA,gBACrGnE,OAAA;QAAKoE,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBAC3CnE,OAAA;UAAKoE,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDnE,OAAA;YAAIoE,SAAS,EAAC,uDAAuD;YAAAD,QAAA,gBACnEnE,OAAA,CAACH,KAAK;cAACuE,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBACpB,EAAC/D,YAAY,CAACS,MAAM,EAAC,GACrC;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxE,OAAA;YAAKoE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,GAC5B9D,QAAQ,iBACPL,OAAA;cACEyE,OAAO,EAAEtD,eAAgB;cACzBuD,QAAQ,EAAE7D,cAAe;cACzBuD,SAAS,EAAC,uDAAuD;cACjEO,KAAK,EAAC,UAAU;cAAAR,QAAA,eAEhBnE,OAAA,CAACJ,SAAS;gBAACwE,SAAS,EAAE,WAAWvD,cAAc,GAAG,cAAc,GAAG,EAAE;cAAG;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CACT,eACDxE,OAAA;cACEyE,OAAO,EAAEvB,iBAAkB;cAC3BkB,SAAS,EAAC,iCAAiC;cAC3CO,KAAK,EAAC,aAAa;cAAAR,QAAA,eAEnBnE,OAAA,CAACF,CAAC;gBAACsE,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELzD,YAAY,iBACXf,OAAA;UAAGoE,SAAS,EAAC,4BAA4B;UAAAD,QAAA,GAAC,aAC7B,EAACpD,YAAY,CAAC6D,kBAAkB,CAAC,CAAC;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxE,OAAA;QAAKoE,SAAS,EAAC,0BAA0B;QAAAD,QAAA,EACtC1D,YAAY,CAACoE,GAAG,CAAC,CAAClB,IAAI,EAAEmB,KAAK,kBAC5B9E,OAAA;UAAiBoE,SAAS,EAAC,8CAA8C;UAAAD,QAAA,eACvEnE,OAAA;YAAKoE,SAAS,EAAC,kCAAkC;YAAAD,QAAA,gBAC/CnE,OAAA;cAAKoE,SAAS,EAAC,QAAQ;cAAAD,QAAA,gBACrBnE,OAAA;gBAAGoE,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAC7CT,kBAAkB,CAACC,IAAI;cAAC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACJxE,OAAA;gBAAGoE,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EACtCZ,eAAe,CAACI,IAAI,CAACH,SAAS;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNxE,OAAA;cAAKoE,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACrCnE,OAAA;gBAAMoE,SAAS,EAAE,uEACfT,IAAI,CAACO,MAAM,KAAK,MAAM,GAClB,6BAA6B,GAC7BP,IAAI,CAACO,MAAM,KAAK,KAAK,GACrB,2BAA2B,GAC3B,yBAAyB,EAC5B;gBAAAC,QAAA,EACAR,IAAI,CAACO;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GArBEM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELnE,QAAQ,IAAII,YAAY,CAACS,MAAM,GAAG,CAAC,iBAClClB,OAAA;QAAKoE,SAAS,EAAC,6BAA6B;QAAAD,QAAA,eAC1CnE,OAAA;UACEyE,OAAO,EAAEtD,eAAgB;UACzBuD,QAAQ,EAAE7D,cAAe;UACzBuD,SAAS,EAAC,+IAA+I;UAAAD,QAAA,EAExJtD,cAAc,gBACbb,OAAA,CAAAE,SAAA;YAAAiE,QAAA,gBACEnE,OAAA,CAACJ,SAAS;cAACwE,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAErD;UAAA,eAAE,CAAC,gBAEHxE,OAAA,CAAAE,SAAA;YAAAiE,QAAA,gBACEnE,OAAA,CAACJ,SAAS;cAACwE,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAExC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA3D,cAAc,iBACbb,OAAA;MAAKoE,SAAS,EAAC,4GAA4G;MAAAD,QAAA,eACzHnE,OAAA;QAAKoE,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChCnE,OAAA,CAACJ,SAAS;UAACwE,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDxE,OAAA;UAAMoE,SAAS,EAAC,qBAAqB;UAAAD,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACpE,EAAA,CAjSID,cAAwB;AAAA4E,EAAA,GAAxB5E,cAAwB;AAmS9B,eAAeA,cAAc;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}