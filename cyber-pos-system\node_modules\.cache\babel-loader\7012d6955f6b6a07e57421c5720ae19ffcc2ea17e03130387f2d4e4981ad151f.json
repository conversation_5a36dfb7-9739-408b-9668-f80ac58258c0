{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m4 6 3-3 3 3\",\n  key: \"9aidw8\"\n}], [\"path\", {\n  d: \"M7 17V3\",\n  key: \"19qxw1\"\n}], [\"path\", {\n  d: \"m14 6 3-3 3 3\",\n  key: \"6iy689\"\n}], [\"path\", {\n  d: \"M17 17V3\",\n  key: \"o0fmgi\"\n}], [\"path\", {\n  d: \"M4 21h16\",\n  key: \"1h09gz\"\n}]];\nconst ArrowsUpFromLine = createLucideIcon(\"arrows-up-from-line\", __iconNode);\nexport { __iconNode, ArrowsUpFromLine as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ArrowsUpFromLine", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\arrows-up-from-line.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm4 6 3-3 3 3', key: '9aidw8' }],\n  ['path', { d: 'M7 17V3', key: '19qxw1' }],\n  ['path', { d: 'm14 6 3-3 3 3', key: '6iy689' }],\n  ['path', { d: 'M17 17V3', key: 'o0fmgi' }],\n  ['path', { d: 'M4 21h16', key: '1h09gz' }],\n];\n\n/**\n * @component @name ArrowsUpFromLine\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNCA2IDMtMyAzIDMiIC8+CiAgPHBhdGggZD0iTTcgMTdWMyIgLz4KICA8cGF0aCBkPSJtMTQgNiAzLTMgMyAzIiAvPgogIDxwYXRoIGQ9Ik0xNyAxN1YzIiAvPgogIDxwYXRoIGQ9Ik00IDIxaDE2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrows-up-from-line\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowsUpFromLine = createLucideIcon('arrows-up-from-line', __iconNode);\n\nexport default ArrowsUpFromLine;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,wBAAuBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}