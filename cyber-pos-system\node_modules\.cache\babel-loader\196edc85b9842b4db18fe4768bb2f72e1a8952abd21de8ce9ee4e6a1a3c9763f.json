{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 9v6\",\n  key: \"158jrl\"\n}], [\"path\", {\n  d: \"M9 9h3V5l7 7-7 7v-4H9V9z\",\n  key: \"1sg2xn\"\n}]];\nconst ArrowBigRightDash = createLucideIcon(\"arrow-big-right-dash\", __iconNode);\nexport { __iconNode, ArrowBigRightDash as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ArrowBigRightDash", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\arrow-big-right-dash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 9v6', key: '158jrl' }],\n  ['path', { d: 'M9 9h3V5l7 7-7 7v-4H9V9z', key: '1sg2xn' }],\n];\n\n/**\n * @component @name ArrowBigRightDash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSA5djYiIC8+CiAgPHBhdGggZD0iTTkgOWgzVjVsNyA3LTcgN3YtNEg5Vjl6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-big-right-dash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowBigRightDash = createLucideIcon('arrow-big-right-dash', __iconNode);\n\nexport default ArrowBigRightDash;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAU,GAC3D;AAaM,MAAAC,iBAAA,GAAoBC,gBAAiB,yBAAwBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}