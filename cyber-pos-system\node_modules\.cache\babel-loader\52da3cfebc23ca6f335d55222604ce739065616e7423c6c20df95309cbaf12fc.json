{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\inventory\\\\ProductModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { X, Package, DollarSign, Calendar, AlertTriangle } from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductModal = ({\n  product,\n  onClose\n}) => {\n  _s();\n  const {\n    createProduct,\n    updateProduct,\n    getProductCategories\n  } = useProducts();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: 0,\n    category: '',\n    stockQuantity: 0,\n    reorderLevel: 0,\n    hasExpiry: false,\n    expiryDate: '',\n    isActive: true\n  });\n  const isEditing = !!product;\n  const categories = getProductCategories();\n  const predefinedCategories = ['Paper', 'Writing', 'Office', 'Printer', 'Electronics', 'Stationery', 'Other'];\n  const allCategories = [...new Set([...predefinedCategories, ...categories])].sort();\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        category: product.category,\n        stockQuantity: product.stockQuantity,\n        reorderLevel: product.reorderLevel,\n        hasExpiry: product.hasExpiry,\n        expiryDate: product.expiryDate ? product.expiryDate.toISOString().split('T')[0] : '',\n        isActive: product.isActive\n      });\n    }\n  }, [product]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n    try {\n      const productData = {\n        ...formData,\n        expiryDate: formData.hasExpiry && formData.expiryDate ? new Date(formData.expiryDate) : undefined\n      };\n      if (isEditing) {\n        await updateProduct(product.id, productData);\n      } else {\n        await createProduct(productData);\n      }\n      onClose();\n    } catch (error) {\n      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} product`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const validateForm = () => {\n    if (!formData.name.trim()) return false;\n    if (!formData.category.trim()) return false;\n    if (formData.price < 0) return false;\n    if (formData.stockQuantity < 0) return false;\n    if (formData.reorderLevel < 0) return false;\n    if (formData.hasExpiry && !formData.expiryDate) return false;\n    return true;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Package, {\n              className: \"h-5 w-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), isEditing ? 'Edit Product' : 'Add New Product']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Product Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: formData.name,\n                onChange: e => setFormData({\n                  ...formData,\n                  name: e.target.value\n                }),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                placeholder: \"e.g., A4 Paper\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: formData.category,\n                onChange: e => setFormData({\n                  ...formData,\n                  category: e.target.value\n                }),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), allCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.description,\n              onChange: e => setFormData({\n                ...formData,\n                description: e.target.value\n              }),\n              rows: 3,\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n              placeholder: \"Brief description of the product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), \"Pricing Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Unit Price (KSh) *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                required: true,\n                min: \"0\",\n                step: \"0.01\",\n                value: formData.price,\n                onChange: e => setFormData({\n                  ...formData,\n                  price: parseFloat(e.target.value) || 0\n                }),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Package, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), \"Inventory Management\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Initial Stock Quantity *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  required: true,\n                  min: \"0\",\n                  value: formData.stockQuantity,\n                  onChange: e => setFormData({\n                    ...formData,\n                    stockQuantity: parseInt(e.target.value) || 0\n                  }),\n                  className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Reorder Level *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  required: true,\n                  min: \"0\",\n                  value: formData.reorderLevel,\n                  onChange: e => setFormData({\n                    ...formData,\n                    reorderLevel: parseInt(e.target.value) || 0\n                  }),\n                  className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Alert when stock falls below this level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-orange-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), \"Expiry Management\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"hasExpiry\",\n                  checked: formData.hasExpiry,\n                  onChange: e => setFormData({\n                    ...formData,\n                    hasExpiry: e.target.checked,\n                    expiryDate: e.target.checked ? formData.expiryDate : ''\n                  }),\n                  className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"hasExpiry\",\n                  className: \"ml-2 block text-sm text-gray-700\",\n                  children: \"This product has an expiry date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), formData.hasExpiry && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Expiry Date *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  required: formData.hasExpiry,\n                  value: formData.expiryDate,\n                  onChange: e => setFormData({\n                    ...formData,\n                    expiryDate: e.target.value\n                  }),\n                  className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                  min: new Date().toISOString().split('T')[0]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"isActive\",\n              checked: formData.isActive,\n              onChange: e => setFormData({\n                ...formData,\n                isActive: e.target.checked\n              }),\n              className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isActive\",\n              className: \"ml-2 block text-sm text-gray-700\",\n              children: \"Product is active and available for sale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), formData.reorderLevel > formData.stockQuantity && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-yellow-50 border border-yellow-200 rounded-md p-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-5 w-5 text-yellow-400 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-yellow-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Warning:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this), \" Reorder level is higher than current stock. This product will immediately show as low stock.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 pt-6 border-t\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: onClose,\n              className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading || !validateForm(),\n              className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: loading ? 'Saving...' : isEditing ? 'Update Product' : 'Create Product'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductModal, \"PlyLrNcukjG7CcirG82ZtwvFBAE=\", false, function () {\n  return [useProducts];\n});\n_c = ProductModal;\nexport default ProductModal;\nvar _c;\n$RefreshReg$(_c, \"ProductModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "Package", "DollarSign", "Calendar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useProducts", "jsxDEV", "_jsxDEV", "ProductModal", "product", "onClose", "_s", "createProduct", "updateProduct", "getProductCategories", "loading", "setLoading", "error", "setError", "formData", "setFormData", "name", "description", "price", "category", "stockQuantity", "reorderLevel", "hasEx<PERSON>ry", "expiryDate", "isActive", "isEditing", "categories", "predefinedCategories", "allCategories", "Set", "sort", "toISOString", "split", "handleSubmit", "e", "preventDefault", "productData", "Date", "undefined", "id", "message", "validateForm", "trim", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "required", "value", "onChange", "target", "placeholder", "map", "rows", "min", "step", "parseFloat", "parseInt", "checked", "htmlFor", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/ProductModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, Package, DollarSign, Calendar, AlertTriangle } from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\n\ninterface ProductModalProps {\n  product: Product | null;\n  onClose: () => void;\n}\n\nconst ProductModal: React.FC<ProductModalProps> = ({ product, onClose }) => {\n  const { createProduct, updateProduct, getProductCategories } = useProducts();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: 0,\n    category: '',\n    stockQuantity: 0,\n    reorderLevel: 0,\n    hasExpiry: false,\n    expiryDate: '',\n    isActive: true\n  });\n\n  const isEditing = !!product;\n  const categories = getProductCategories();\n  \n  const predefinedCategories = [\n    'Paper',\n    'Writing',\n    'Office',\n    'Printer',\n    'Electronics',\n    'Stationery',\n    'Other'\n  ];\n\n  const allCategories = [...new Set([...predefinedCategories, ...categories])].sort();\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        category: product.category,\n        stockQuantity: product.stockQuantity,\n        reorderLevel: product.reorderLevel,\n        hasExpiry: product.hasExpiry,\n        expiryDate: product.expiryDate ? product.expiryDate.toISOString().split('T')[0] : '',\n        isActive: product.isActive\n      });\n    }\n  }, [product]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const productData = {\n        ...formData,\n        expiryDate: formData.hasExpiry && formData.expiryDate ? new Date(formData.expiryDate) : undefined\n      };\n\n      if (isEditing) {\n        await updateProduct(product.id, productData);\n      } else {\n        await createProduct(productData);\n      }\n\n      onClose();\n    } catch (error: any) {\n      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} product`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const validateForm = () => {\n    if (!formData.name.trim()) return false;\n    if (!formData.category.trim()) return false;\n    if (formData.price < 0) return false;\n    if (formData.stockQuantity < 0) return false;\n    if (formData.reorderLevel < 0) return false;\n    if (formData.hasExpiry && !formData.expiryDate) return false;\n    return true;\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n              <Package className=\"h-5 w-5 mr-2\" />\n              {isEditing ? 'Edit Product' : 'Add New Product'}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Product Name *\n                </label>\n                <input\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  placeholder=\"e.g., A4 Paper\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category *\n                </label>\n                <select\n                  required\n                  value={formData.category}\n                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                >\n                  <option value=\"\">Select Category</option>\n                  {allCategories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                rows={3}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"Brief description of the product\"\n              />\n            </div>\n\n            {/* Pricing */}\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <DollarSign className=\"h-4 w-4 mr-2\" />\n                Pricing Information\n              </h4>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Unit Price (KSh) *\n                </label>\n                <input\n                  type=\"number\"\n                  required\n                  min=\"0\"\n                  step=\"0.01\"\n                  value={formData.price}\n                  onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                />\n              </div>\n            </div>\n\n            {/* Inventory Management */}\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <Package className=\"h-4 w-4 mr-2\" />\n                Inventory Management\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Initial Stock Quantity *\n                  </label>\n                  <input\n                    type=\"number\"\n                    required\n                    min=\"0\"\n                    value={formData.stockQuantity}\n                    onChange={(e) => setFormData({ ...formData, stockQuantity: parseInt(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Reorder Level *\n                  </label>\n                  <input\n                    type=\"number\"\n                    required\n                    min=\"0\"\n                    value={formData.reorderLevel}\n                    onChange={(e) => setFormData({ ...formData, reorderLevel: parseInt(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Alert when stock falls below this level\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Expiry Management */}\n            <div className=\"bg-orange-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <Calendar className=\"h-4 w-4 mr-2\" />\n                Expiry Management\n              </h4>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"hasExpiry\"\n                    checked={formData.hasExpiry}\n                    onChange={(e) => setFormData({ \n                      ...formData, \n                      hasExpiry: e.target.checked,\n                      expiryDate: e.target.checked ? formData.expiryDate : ''\n                    })}\n                    className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"hasExpiry\" className=\"ml-2 block text-sm text-gray-700\">\n                    This product has an expiry date\n                  </label>\n                </div>\n                \n                {formData.hasExpiry && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Expiry Date *\n                    </label>\n                    <input\n                      type=\"date\"\n                      required={formData.hasExpiry}\n                      value={formData.expiryDate}\n                      onChange={(e) => setFormData({ ...formData, expiryDate: e.target.value })}\n                      className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                      min={new Date().toISOString().split('T')[0]}\n                    />\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Status */}\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"isActive\"\n                checked={formData.isActive}\n                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"isActive\" className=\"ml-2 block text-sm text-gray-700\">\n                Product is active and available for sale\n              </label>\n            </div>\n\n            {/* Validation Warning */}\n            {formData.reorderLevel > formData.stockQuantity && (\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-3\">\n                <div className=\"flex\">\n                  <AlertTriangle className=\"h-5 w-5 text-yellow-400 mr-2\" />\n                  <div className=\"text-sm text-yellow-700\">\n                    <strong>Warning:</strong> Reorder level is higher than current stock. \n                    This product will immediately show as low stock.\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Actions */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading || !validateForm()}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Saving...' : (isEditing ? 'Update Product' : 'Create Product')}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,CAAC,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,cAAc;AAC9E,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQtD,MAAMC,YAAyC,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAqB,CAAC,GAAGT,WAAW,CAAC,CAAC;EAC5E,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAG,CAAC,CAACrB,OAAO;EAC3B,MAAMsB,UAAU,GAAGjB,oBAAoB,CAAC,CAAC;EAEzC,MAAMkB,oBAAoB,GAAG,CAC3B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,YAAY,EACZ,OAAO,CACR;EAED,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGF,oBAAoB,EAAE,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC;EAEnFpC,SAAS,CAAC,MAAM;IACd,IAAIU,OAAO,EAAE;MACXW,WAAW,CAAC;QACVC,IAAI,EAAEZ,OAAO,CAACY,IAAI;QAClBC,WAAW,EAAEb,OAAO,CAACa,WAAW;QAChCC,KAAK,EAAEd,OAAO,CAACc,KAAK;QACpBC,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1BC,aAAa,EAAEhB,OAAO,CAACgB,aAAa;QACpCC,YAAY,EAAEjB,OAAO,CAACiB,YAAY;QAClCC,SAAS,EAAElB,OAAO,CAACkB,SAAS;QAC5BC,UAAU,EAAEnB,OAAO,CAACmB,UAAU,GAAGnB,OAAO,CAACmB,UAAU,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QACpFR,QAAQ,EAAEpB,OAAO,CAACoB;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,OAAO,CAAC,CAAC;EAEb,MAAM6B,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBtB,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMyB,WAAW,GAAG;QAClB,GAAGtB,QAAQ;QACXS,UAAU,EAAET,QAAQ,CAACQ,SAAS,IAAIR,QAAQ,CAACS,UAAU,GAAG,IAAIc,IAAI,CAACvB,QAAQ,CAACS,UAAU,CAAC,GAAGe;MAC1F,CAAC;MAED,IAAIb,SAAS,EAAE;QACb,MAAMjB,aAAa,CAACJ,OAAO,CAACmC,EAAE,EAAEH,WAAW,CAAC;MAC9C,CAAC,MAAM;QACL,MAAM7B,aAAa,CAAC6B,WAAW,CAAC;MAClC;MAEA/B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOO,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4B,OAAO,IAAI,aAAaf,SAAS,GAAG,QAAQ,GAAG,QAAQ,UAAU,CAAC;IACnF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC3B,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK;IACvC,IAAI,CAAC5B,QAAQ,CAACK,QAAQ,CAACuB,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAI5B,QAAQ,CAACI,KAAK,GAAG,CAAC,EAAE,OAAO,KAAK;IACpC,IAAIJ,QAAQ,CAACM,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IAC5C,IAAIN,QAAQ,CAACO,YAAY,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAIP,QAAQ,CAACQ,SAAS,IAAI,CAACR,QAAQ,CAACS,UAAU,EAAE,OAAO,KAAK;IAC5D,OAAO,IAAI;EACb,CAAC;EAED,oBACErB,OAAA;IAAKyC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzF1C,OAAA;MAAKyC,SAAS,EAAC,mFAAmF;MAAAC,QAAA,eAChG1C,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1C,OAAA;UAAKyC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD1C,OAAA;YAAIyC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBACjE1C,OAAA,CAACN,OAAO;cAAC+C,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnCvB,SAAS,GAAG,cAAc,GAAG,iBAAiB;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACL9C,OAAA;YACE+C,OAAO,EAAE5C,OAAQ;YACjBsC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C1C,OAAA,CAACP,CAAC;cAACgD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELpC,KAAK,iBACJV,OAAA;UAAKyC,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFhC;QAAK;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED9C,OAAA;UAAMgD,QAAQ,EAAEjB,YAAa;UAACU,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEjD1C,OAAA;YAAKyC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1C,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAOyC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9C,OAAA;gBACEiD,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRC,KAAK,EAAEvC,QAAQ,CAACE,IAAK;gBACrBsC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEE,IAAI,EAAEkB,CAAC,CAACqB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACpEV,SAAS,EAAC,uHAAuH;gBACjIa,WAAW,EAAC;cAAgB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9C,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAOyC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9C,OAAA;gBACEkD,QAAQ;gBACRC,KAAK,EAAEvC,QAAQ,CAACK,QAAS;gBACzBmC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,QAAQ,EAAEe,CAAC,CAACqB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACxEV,SAAS,EAAC,uHAAuH;gBAAAC,QAAA,gBAEjI1C,OAAA;kBAAQmD,KAAK,EAAC,EAAE;kBAAAT,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCpB,aAAa,CAAC6B,GAAG,CAACtC,QAAQ,iBACzBjB,OAAA;kBAAuBmD,KAAK,EAAElC,QAAS;kBAAAyB,QAAA,EAAEzB;gBAAQ,GAApCA,QAAQ;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOyC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEmD,KAAK,EAAEvC,QAAQ,CAACG,WAAY;cAC5BqC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEiB,CAAC,CAACqB,MAAM,CAACF;cAAM,CAAC,CAAE;cAC3EK,IAAI,EAAE,CAAE;cACRf,SAAS,EAAC,uHAAuH;cACjIa,WAAW,EAAC;YAAkC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9C,OAAA;YAAKyC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxC1C,OAAA;cAAIyC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtE1C,OAAA,CAACL,UAAU;gBAAC8C,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL9C,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAOyC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9C,OAAA;gBACEiD,IAAI,EAAC,QAAQ;gBACbC,QAAQ;gBACRO,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACXP,KAAK,EAAEvC,QAAQ,CAACI,KAAM;gBACtBoC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,KAAK,EAAE2C,UAAU,CAAC3B,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAC,IAAI;gBAAE,CAAC,CAAE;gBACtFV,SAAS,EAAC;cAAuH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9C,OAAA;YAAKyC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxC1C,OAAA;cAAIyC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtE1C,OAAA,CAACN,OAAO;gBAAC+C,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL9C,OAAA;cAAKyC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAOyC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9C,OAAA;kBACEiD,IAAI,EAAC,QAAQ;kBACbC,QAAQ;kBACRO,GAAG,EAAC,GAAG;kBACPN,KAAK,EAAEvC,QAAQ,CAACM,aAAc;kBAC9BkC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEM,aAAa,EAAE0C,QAAQ,CAAC5B,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAE;kBAC5FV,SAAS,EAAC;gBAAuH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAOyC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9C,OAAA;kBACEiD,IAAI,EAAC,QAAQ;kBACbC,QAAQ;kBACRO,GAAG,EAAC,GAAG;kBACPN,KAAK,EAAEvC,QAAQ,CAACO,YAAa;kBAC7BiC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEO,YAAY,EAAEyC,QAAQ,CAAC5B,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAE;kBAC3FV,SAAS,EAAC;gBAAuH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClI,CAAC,eACF9C,OAAA;kBAAGyC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9C,OAAA;YAAKyC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1C,OAAA;cAAIyC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtE1C,OAAA,CAACJ,QAAQ;gBAAC6C,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL9C,OAAA;cAAKyC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1C,OAAA;gBAAKyC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1C,OAAA;kBACEiD,IAAI,EAAC,UAAU;kBACfZ,EAAE,EAAC,WAAW;kBACdwB,OAAO,EAAEjD,QAAQ,CAACQ,SAAU;kBAC5BgC,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;oBAC3B,GAAGD,QAAQ;oBACXQ,SAAS,EAAEY,CAAC,CAACqB,MAAM,CAACQ,OAAO;oBAC3BxC,UAAU,EAAEW,CAAC,CAACqB,MAAM,CAACQ,OAAO,GAAGjD,QAAQ,CAACS,UAAU,GAAG;kBACvD,CAAC,CAAE;kBACHoB,SAAS,EAAC;gBAAyE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACF9C,OAAA;kBAAO8D,OAAO,EAAC,WAAW;kBAACrB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAExE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAELlC,QAAQ,CAACQ,SAAS,iBACjBpB,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAOyC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9C,OAAA;kBACEiD,IAAI,EAAC,MAAM;kBACXC,QAAQ,EAAEtC,QAAQ,CAACQ,SAAU;kBAC7B+B,KAAK,EAAEvC,QAAQ,CAACS,UAAW;kBAC3B+B,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAES,UAAU,EAAEW,CAAC,CAACqB,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC1EV,SAAS,EAAC,uHAAuH;kBACjIgB,GAAG,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACN,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9C,OAAA;YAAKyC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1C,OAAA;cACEiD,IAAI,EAAC,UAAU;cACfZ,EAAE,EAAC,UAAU;cACbwB,OAAO,EAAEjD,QAAQ,CAACU,QAAS;cAC3B8B,QAAQ,EAAGpB,CAAC,IAAKnB,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEU,QAAQ,EAAEU,CAAC,CAACqB,MAAM,CAACQ;cAAQ,CAAC,CAAE;cAC1EpB,SAAS,EAAC;YAAyE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eACF9C,OAAA;cAAO8D,OAAO,EAAC,UAAU;cAACrB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAGLlC,QAAQ,CAACO,YAAY,GAAGP,QAAQ,CAACM,aAAa,iBAC7ClB,OAAA;YAAKyC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnE1C,OAAA;cAAKyC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB1C,OAAA,CAACH,aAAa;gBAAC4C,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D9C,OAAA;gBAAKyC,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC1C,OAAA;kBAAA0C,QAAA,EAAQ;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iGAE3B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD9C,OAAA;YAAKyC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvD1C,OAAA;cACEiD,IAAI,EAAC,QAAQ;cACbF,OAAO,EAAE5C,OAAQ;cACjBsC,SAAS,EAAC,uJAAuJ;cAAAC,QAAA,EAClK;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA;cACEiD,IAAI,EAAC,QAAQ;cACbc,QAAQ,EAAEvD,OAAO,IAAI,CAAC+B,YAAY,CAAC,CAAE;cACrCE,SAAS,EAAC,gMAAgM;cAAAC,QAAA,EAEzMlC,OAAO,GAAG,WAAW,GAAIe,SAAS,GAAG,gBAAgB,GAAG;YAAiB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAtTIH,YAAyC;EAAA,QACkBH,WAAW;AAAA;AAAAkE,EAAA,GADtE/D,YAAyC;AAwT/C,eAAeA,YAAY;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}