import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Eye, EyeOff, Monitor, Database } from 'lucide-react';
import { initializeDemoData, createDemoUsers, createAdminUser } from '../../utils/seedData';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [initializingDemo, setInitializingDemo] = useState(false);
  const [creatingAdmin, setCreatingAdmin] = useState(false);

  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(email, password);
    } catch (error: any) {
      setError(error.message || 'Failed to login');
    } finally {
      setLoading(false);
    }
  };

  const handleInitializeDemo = async () => {
    setError('');
    setInitializingDemo(true);

    try {
      await initializeDemoData();
      await createDemoUsers();
      setError('Demo data initialized successfully! You can now <NAME_EMAIL> / password');
    } catch (error: any) {
      setError(error.message || 'Failed to initialize demo data');
    } finally {
      setInitializingDemo(false);
    }
  };

  const handleCreateAdmin = async () => {
    setError('');
    setCreatingAdmin(true);

    try {
      await createAdminUser();
      setError('Admin user created successfully! You can now <NAME_EMAIL> / password');
    } catch (error: any) {
      setError(error.message || 'Failed to create admin user');
    } finally {
      setCreatingAdmin(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
              <Monitor className="h-6 w-6 text-white" />
            </div>
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              Cyber POS System
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Sign in to your account
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter your email"
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="mt-1 relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </button>
            </div>
          </form>
          
          <div className="mt-6 space-y-4">
            <div className="text-center space-y-2">
              <button
                type="button"
                onClick={handleCreateAdmin}
                disabled={creatingAdmin}
                className="inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 mr-2"
              >
                <Database className="h-4 w-4 mr-2" />
                {creatingAdmin ? 'Creating...' : 'Create Admin User'}
              </button>
              <button
                type="button"
                onClick={handleInitializeDemo}
                disabled={initializingDemo}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                <Database className="h-4 w-4 mr-2" />
                {initializingDemo ? 'Initializing...' : 'Initialize Demo Data'}
              </button>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500">
                Demo credentials: <EMAIL> / password
              </p>
              <p className="text-xs text-gray-400 mt-1">
                For production: Create admin user first, then login and initialize demo data
              </p>
              <p className="text-xs text-gray-400">
                For development: Use Firebase emulators and initialize demo data directly
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
