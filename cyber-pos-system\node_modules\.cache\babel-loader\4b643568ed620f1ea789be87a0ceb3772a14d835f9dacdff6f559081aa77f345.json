{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 22V2L7 7l10 5\",\n  key: \"1rmf0r\"\n}]];\nconst FlagTriangleLeft = createLucideIcon(\"flag-triangle-left\", __iconNode);\nexport { __iconNode, FlagTriangleLeft as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FlagTriangleLeft", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\flag-triangle-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M17 22V2L7 7l10 5', key: '1rmf0r' }]];\n\n/**\n * @component @name FlagTriangleLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgMjJWMkw3IDdsMTAgNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/flag-triangle-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlagTriangleLeft = createLucideIcon('flag-triangle-left', __iconNode);\n\nexport default FlagTriangleLeft;\n"], "mappings": ";;;;;;;;AAGa,MAAAA,UAAA,GAAuB,CAAC,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAS,CAAC,CAAC;AAalF,MAAAC,gBAAA,GAAmBC,gBAAiB,uBAAsBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}