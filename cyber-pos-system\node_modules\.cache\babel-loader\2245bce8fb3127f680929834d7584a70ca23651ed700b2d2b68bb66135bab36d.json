{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\services\\\\ServiceModal.tsx\";\nimport React from 'react';\nimport { X, DollarSign, Link } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceModal = ({\n  service,\n  formData,\n  setFormData,\n  onSubmit,\n  onClose,\n  allServices\n}) => {\n  const isEditing = !!service;\n  const availableServices = allServices.filter(s => s.id !== (service === null || service === void 0 ? void 0 : service.id) && s.isActive);\n  const predefinedCategories = ['Printing', 'Scanning', 'Typing', 'Internet', 'Government', 'Office', 'Other'];\n  const handleBundledServiceToggle = serviceId => {\n    const currentBundled = formData.bundledServices || [];\n    if (currentBundled.includes(serviceId)) {\n      setFormData({\n        ...formData,\n        bundledServices: currentBundled.filter(id => id !== serviceId)\n      });\n    } else {\n      setFormData({\n        ...formData,\n        bundledServices: [...currentBundled, serviceId]\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: isEditing ? 'Edit Service' : 'Create New Service'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: onSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Service Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                required: true,\n                value: formData.name,\n                onChange: e => setFormData({\n                  ...formData,\n                  name: e.target.value\n                }),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                placeholder: \"e.g., Document Printing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                required: true,\n                value: formData.category,\n                onChange: e => setFormData({\n                  ...formData,\n                  category: e.target.value\n                }),\n                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this), predefinedCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.description,\n              onChange: e => setFormData({\n                ...formData,\n                description: e.target.value\n              }),\n              rows: 3,\n              className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n              placeholder: \"Brief description of the service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), \"Pricing Configuration\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Base Price (KSh) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  required: true,\n                  min: \"0\",\n                  step: \"0.01\",\n                  value: formData.basePrice,\n                  onChange: e => setFormData({\n                    ...formData,\n                    basePrice: parseFloat(e.target.value) || 0\n                  }),\n                  className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"allowPriceOverride\",\n                  checked: formData.allowPriceOverride,\n                  onChange: e => setFormData({\n                    ...formData,\n                    allowPriceOverride: e.target.checked\n                  }),\n                  className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"allowPriceOverride\",\n                  className: \"ml-2 block text-sm text-gray-700\",\n                  children: \"Allow price override at POS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), availableServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-900 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), \"Service Bundling\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-3\",\n              children: \"Select services that come free with this service (e.g., free emailing with typing)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto\",\n              children: availableServices.map(availableService => {\n                var _formData$bundledServ;\n                return /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: ((_formData$bundledServ = formData.bundledServices) === null || _formData$bundledServ === void 0 ? void 0 : _formData$bundledServ.includes(availableService.id)) || false,\n                    onChange: () => handleBundledServiceToggle(availableService.id),\n                    className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-700\",\n                    children: [availableService.name, \" (KSh \", availableService.basePrice, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)]\n                }, availableService.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"isActive\",\n              checked: formData.isActive,\n              onChange: e => setFormData({\n                ...formData,\n                isActive: e.target.checked\n              }),\n              className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isActive\",\n              className: \"ml-2 block text-sm text-gray-700\",\n              children: \"Service is active and available for sale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 pt-6 border-t\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: onClose,\n              className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n              children: isEditing ? 'Update Service' : 'Create Service'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_c = ServiceModal;\nexport default ServiceModal;\nvar _c;\n$RefreshReg$(_c, \"ServiceModal\");", "map": {"version": 3, "names": ["React", "X", "DollarSign", "Link", "jsxDEV", "_jsxDEV", "ServiceModal", "service", "formData", "setFormData", "onSubmit", "onClose", "allServices", "isEditing", "availableServices", "filter", "s", "id", "isActive", "predefinedCategories", "handleBundledServiceToggle", "serviceId", "currentBundled", "bundledServices", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "required", "value", "name", "onChange", "e", "target", "placeholder", "category", "map", "description", "rows", "min", "step", "basePrice", "parseFloat", "checked", "allowPriceOverride", "htmlFor", "length", "availableService", "_formData$bundledServ", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/services/ServiceModal.tsx"], "sourcesContent": ["import React from 'react';\nimport { X, DollarSign, Tag, Link } from 'lucide-react';\nimport { Service } from '../../types';\n\ninterface ServiceModalProps {\n  service: Service | null;\n  formData: {\n    name: string;\n    description: string;\n    basePrice: number;\n    category: string;\n    allowPriceOverride: boolean;\n    bundledServices: string[];\n    isActive: boolean;\n  };\n  setFormData: React.Dispatch<React.SetStateAction<{\n    name: string;\n    description: string;\n    basePrice: number;\n    category: string;\n    allowPriceOverride: boolean;\n    bundledServices: string[];\n    isActive: boolean;\n  }>>;\n  onSubmit: (e: React.FormEvent) => void;\n  onClose: () => void;\n  allServices: Service[];\n}\n\nconst ServiceModal: React.FC<ServiceModalProps> = ({\n  service,\n  formData,\n  setFormData,\n  onSubmit,\n  onClose,\n  allServices\n}) => {\n  const isEditing = !!service;\n  const availableServices = allServices.filter(s => s.id !== service?.id && s.isActive);\n  \n  const predefinedCategories = [\n    'Printing',\n    'Scanning', \n    'Typing',\n    'Internet',\n    'Government',\n    'Office',\n    'Other'\n  ];\n\n  const handleBundledServiceToggle = (serviceId: string) => {\n    const currentBundled = formData.bundledServices || [];\n    if (currentBundled.includes(serviceId)) {\n      setFormData({\n        ...formData,\n        bundledServices: currentBundled.filter(id => id !== serviceId)\n      });\n    } else {\n      setFormData({\n        ...formData,\n        bundledServices: [...currentBundled, serviceId]\n      });\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {isEditing ? 'Edit Service' : 'Create New Service'}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          </div>\n          \n          <form onSubmit={onSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Service Name *\n                </label>\n                <input\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  placeholder=\"e.g., Document Printing\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category *\n                </label>\n                <select\n                  required\n                  value={formData.category}\n                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                >\n                  <option value=\"\">Select Category</option>\n                  {predefinedCategories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                rows={3}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                placeholder=\"Brief description of the service\"\n              />\n            </div>\n\n            {/* Pricing */}\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                <DollarSign className=\"h-4 w-4 mr-2\" />\n                Pricing Configuration\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Base Price (KSh) *\n                  </label>\n                  <input\n                    type=\"number\"\n                    required\n                    min=\"0\"\n                    step=\"0.01\"\n                    value={formData.basePrice}\n                    onChange={(e) => setFormData({ ...formData, basePrice: parseFloat(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                  />\n                </div>\n                \n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"allowPriceOverride\"\n                    checked={formData.allowPriceOverride}\n                    onChange={(e) => setFormData({ ...formData, allowPriceOverride: e.target.checked })}\n                    className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"allowPriceOverride\" className=\"ml-2 block text-sm text-gray-700\">\n                    Allow price override at POS\n                  </label>\n                </div>\n              </div>\n            </div>\n\n            {/* Service Bundling */}\n            {availableServices.length > 0 && (\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                  <Link className=\"h-4 w-4 mr-2\" />\n                  Service Bundling\n                </h4>\n                <p className=\"text-xs text-gray-600 mb-3\">\n                  Select services that come free with this service (e.g., free emailing with typing)\n                </p>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto\">\n                  {availableServices.map(availableService => (\n                    <label key={availableService.id} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.bundledServices?.includes(availableService.id) || false}\n                        onChange={() => handleBundledServiceToggle(availableService.id)}\n                        className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                      />\n                      <span className=\"ml-2 text-sm text-gray-700\">\n                        {availableService.name} (KSh {availableService.basePrice})\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Status */}\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"isActive\"\n                checked={formData.isActive}\n                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"isActive\" className=\"ml-2 block text-sm text-gray-700\">\n                Service is active and available for sale\n              </label>\n            </div>\n            \n            {/* Actions */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                {isEditing ? 'Update Service' : 'Create Service'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ServiceModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,CAAC,EAAEC,UAAU,EAAOC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4BxD,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,OAAO;EACPC,QAAQ;EACRC,WAAW;EACXC,QAAQ;EACRC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAG,CAAC,CAACN,OAAO;EAC3B,MAAMO,iBAAiB,GAAGF,WAAW,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,MAAKV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,EAAE,KAAID,CAAC,CAACE,QAAQ,CAAC;EAErF,MAAMC,oBAAoB,GAAG,CAC3B,UAAU,EACV,UAAU,EACV,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,OAAO,CACR;EAED,MAAMC,0BAA0B,GAAIC,SAAiB,IAAK;IACxD,MAAMC,cAAc,GAAGd,QAAQ,CAACe,eAAe,IAAI,EAAE;IACrD,IAAID,cAAc,CAACE,QAAQ,CAACH,SAAS,CAAC,EAAE;MACtCZ,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXe,eAAe,EAAED,cAAc,CAACP,MAAM,CAACE,EAAE,IAAIA,EAAE,KAAKI,SAAS;MAC/D,CAAC,CAAC;IACJ,CAAC,MAAM;MACLZ,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXe,eAAe,EAAE,CAAC,GAAGD,cAAc,EAAED,SAAS;MAChD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKoB,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFrB,OAAA;MAAKoB,SAAS,EAAC,mFAAmF;MAAAC,QAAA,eAChGrB,OAAA;QAAKoB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrB,OAAA;UAAKoB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDrB,OAAA;YAAIoB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9Cb,SAAS,GAAG,cAAc,GAAG;UAAoB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACLzB,OAAA;YACE0B,OAAO,EAAEpB,OAAQ;YACjBc,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CrB,OAAA,CAACJ,CAAC;cAACwB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzB,OAAA;UAAMK,QAAQ,EAAEA,QAAS;UAACe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAE7CrB,OAAA;YAAKoB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDrB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzB,OAAA;gBACE2B,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRC,KAAK,EAAE1B,QAAQ,CAAC2B,IAAK;gBACrBC,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAE2B,IAAI,EAAEE,CAAC,CAACC,MAAM,CAACJ;gBAAM,CAAC,CAAE;gBACpET,SAAS,EAAC,uHAAuH;gBACjIc,WAAW,EAAC;cAAyB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzB,OAAA;gBACE4B,QAAQ;gBACRC,KAAK,EAAE1B,QAAQ,CAACgC,QAAS;gBACzBJ,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEgC,QAAQ,EAAEH,CAAC,CAACC,MAAM,CAACJ;gBAAM,CAAC,CAAE;gBACxET,SAAS,EAAC,uHAAuH;gBAAAC,QAAA,gBAEjIrB,OAAA;kBAAQ6B,KAAK,EAAC,EAAE;kBAAAR,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCX,oBAAoB,CAACsB,GAAG,CAACD,QAAQ,iBAChCnC,OAAA;kBAAuB6B,KAAK,EAAEM,QAAS;kBAAAd,QAAA,EAAEc;gBAAQ,GAApCA,QAAQ;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAOoB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzB,OAAA;cACE6B,KAAK,EAAE1B,QAAQ,CAACkC,WAAY;cAC5BN,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEkC,WAAW,EAAEL,CAAC,CAACC,MAAM,CAACJ;cAAM,CAAC,CAAE;cAC3ES,IAAI,EAAE,CAAE;cACRlB,SAAS,EAAC,uHAAuH;cACjIc,WAAW,EAAC;YAAkC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzB,OAAA;YAAKoB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCrB,OAAA;cAAIoB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtErB,OAAA,CAACH,UAAU;gBAACuB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELzB,OAAA;cAAKoB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAOoB,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRzB,OAAA;kBACE2B,IAAI,EAAC,QAAQ;kBACbC,QAAQ;kBACRW,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,MAAM;kBACXX,KAAK,EAAE1B,QAAQ,CAACsC,SAAU;kBAC1BV,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEsC,SAAS,EAAEC,UAAU,CAACV,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAE;kBAC1FT,SAAS,EAAC;gBAAuH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzB,OAAA;gBAAKoB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrB,OAAA;kBACE2B,IAAI,EAAC,UAAU;kBACff,EAAE,EAAC,oBAAoB;kBACvB+B,OAAO,EAAExC,QAAQ,CAACyC,kBAAmB;kBACrCb,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEyC,kBAAkB,EAAEZ,CAAC,CAACC,MAAM,CAACU;kBAAQ,CAAC,CAAE;kBACpFvB,SAAS,EAAC;gBAAyE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACFzB,OAAA;kBAAO6C,OAAO,EAAC,oBAAoB;kBAACzB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEjF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLhB,iBAAiB,CAACqC,MAAM,GAAG,CAAC,iBAC3B9C,OAAA;YAAKoB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCrB,OAAA;cAAIoB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtErB,OAAA,CAACF,IAAI;gBAACsB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJzB,OAAA;cAAKoB,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAC5EZ,iBAAiB,CAAC2B,GAAG,CAACW,gBAAgB;gBAAA,IAAAC,qBAAA;gBAAA,oBACrChD,OAAA;kBAAiCoB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC5DrB,OAAA;oBACE2B,IAAI,EAAC,UAAU;oBACfgB,OAAO,EAAE,EAAAK,qBAAA,GAAA7C,QAAQ,CAACe,eAAe,cAAA8B,qBAAA,uBAAxBA,qBAAA,CAA0B7B,QAAQ,CAAC4B,gBAAgB,CAACnC,EAAE,CAAC,KAAI,KAAM;oBAC1EmB,QAAQ,EAAEA,CAAA,KAAMhB,0BAA0B,CAACgC,gBAAgB,CAACnC,EAAE,CAAE;oBAChEQ,SAAS,EAAC;kBAAyE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACFzB,OAAA;oBAAMoB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GACzC0B,gBAAgB,CAACjB,IAAI,EAAC,QAAM,EAACiB,gBAAgB,CAACN,SAAS,EAAC,GAC3D;kBAAA;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GATGsB,gBAAgB,CAACnC,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUxB,CAAC;cAAA,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDzB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cACE2B,IAAI,EAAC,UAAU;cACff,EAAE,EAAC,UAAU;cACb+B,OAAO,EAAExC,QAAQ,CAACU,QAAS;cAC3BkB,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEU,QAAQ,EAAEmB,CAAC,CAACC,MAAM,CAACU;cAAQ,CAAC,CAAE;cAC1EvB,SAAS,EAAC;YAAyE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eACFzB,OAAA;cAAO6C,OAAO,EAAC,UAAU;cAACzB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNzB,OAAA;YAAKoB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDrB,OAAA;cACE2B,IAAI,EAAC,QAAQ;cACbD,OAAO,EAAEpB,OAAQ;cACjBc,SAAS,EAAC,uJAAuJ;cAAAC,QAAA,EAClK;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzB,OAAA;cACE2B,IAAI,EAAC,QAAQ;cACbP,SAAS,EAAC,gJAAgJ;cAAAC,QAAA,EAEzJb,SAAS,GAAG,gBAAgB,GAAG;YAAgB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACwB,EAAA,GA1MIhD,YAAyC;AA4M/C,eAAeA,YAAY;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}