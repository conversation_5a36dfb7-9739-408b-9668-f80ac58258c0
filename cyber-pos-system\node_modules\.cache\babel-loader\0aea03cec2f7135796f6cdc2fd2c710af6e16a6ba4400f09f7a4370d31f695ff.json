{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 22H4a2 2 0 0 1 0-4h12\",\n  key: \"bt3f23\"\n}], [\"path\", {\n  d: \"M13.236 18a3 3 0 0 0-2.2-5\",\n  key: \"1tbvmo\"\n}], [\"path\", {\n  d: \"M16 9h.01\",\n  key: \"1bdo4e\"\n}], [\"path\", {\n  d: \"M16.82 3.94a3 3 0 1 1 3.237 4.868l1.815 2.587a1.5 1.5 0 0 1-1.5 2.1l-2.872-.453a3 3 0 0 0-3.5 3\",\n  key: \"9ch7kn\"\n}], [\"path\", {\n  d: \"M17 4.988a3 3 0 1 0-5.2 2.052A7 7 0 0 0 4 14.015 4 4 0 0 0 8 18\",\n  key: \"3s7e9i\"\n}]];\nconst Rat = createLucideIcon(\"rat\", __iconNode);\nexport { __iconNode, Rat as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Rat", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\rat.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13 22H4a2 2 0 0 1 0-4h12', key: 'bt3f23' }],\n  ['path', { d: 'M13.236 18a3 3 0 0 0-2.2-5', key: '1tbvmo' }],\n  ['path', { d: 'M16 9h.01', key: '1bdo4e' }],\n  [\n    'path',\n    {\n      d: 'M16.82 3.94a3 3 0 1 1 3.237 4.868l1.815 2.587a1.5 1.5 0 0 1-1.5 2.1l-2.872-.453a3 3 0 0 0-3.5 3',\n      key: '9ch7kn',\n    },\n  ],\n  ['path', { d: 'M17 4.988a3 3 0 1 0-5.2 2.052A7 7 0 0 0 4 14.015 4 4 0 0 0 8 18', key: '3s7e9i' }],\n];\n\n/**\n * @component @name Rat\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgMjJINGEyIDIgMCAwIDEgMC00aDEyIiAvPgogIDxwYXRoIGQ9Ik0xMy4yMzYgMThhMyAzIDAgMCAwLTIuMi01IiAvPgogIDxwYXRoIGQ9Ik0xNiA5aC4wMSIgLz4KICA8cGF0aCBkPSJNMTYuODIgMy45NGEzIDMgMCAxIDEgMy4yMzcgNC44NjhsMS44MTUgMi41ODdhMS41IDEuNSAwIDAgMS0xLjUgMi4xbC0yLjg3Mi0uNDUzYTMgMyAwIDAgMC0zLjUgMyIgLz4KICA8cGF0aCBkPSJNMTcgNC45ODhhMyAzIDAgMSAwLTUuMiAyLjA1MkE3IDcgMCAwIDAgNCAxNC4wMTUgNCA0IDAgMCAwIDggMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rat\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rat = createLucideIcon('rat', __iconNode);\n\nexport default Rat;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,iEAAmE;EAAAC,GAAA,EAAK;AAAU,GAClG;AAaM,MAAAC,GAAA,GAAMC,gBAAiB,QAAOJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}