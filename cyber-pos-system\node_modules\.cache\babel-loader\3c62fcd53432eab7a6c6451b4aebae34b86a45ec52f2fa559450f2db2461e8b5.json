{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\services\\\\ServiceCategories.tsx\";\nimport React from 'react';\nimport { Tag, Monitor, Printer, Globe, FileText, Building, Wrench } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceCategories = ({\n  categories,\n  selectedCategory,\n  onCategorySelect,\n  serviceCounts\n}) => {\n  const getCategoryIcon = category => {\n    switch (category.toLowerCase()) {\n      case 'printing':\n        return /*#__PURE__*/_jsxDEV(Printer, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 16\n        }, this);\n      case 'scanning':\n        return /*#__PURE__*/_jsxDEV(Monitor, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 16\n        }, this);\n      case 'internet':\n        return /*#__PURE__*/_jsxDEV(Globe, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 16\n        }, this);\n      case 'typing':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 16\n        }, this);\n      case 'government':\n        return /*#__PURE__*/_jsxDEV(Building, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 16\n        }, this);\n      case 'office':\n        return /*#__PURE__*/_jsxDEV(Wrench, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getCategoryColor = category => {\n    switch (category.toLowerCase()) {\n      case 'printing':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      case 'scanning':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'internet':\n        return 'bg-purple-100 text-purple-800 border-purple-200';\n      case 'typing':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'government':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'office':\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n      default:\n        return 'bg-indigo-100 text-indigo-800 border-indigo-200';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        className: \"h-5 w-5 mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), \"Service Categories\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onCategorySelect(''),\n        className: `w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${selectedCategory === '' ? 'bg-primary-50 text-primary-700 border-primary-200' : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Tag, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"All Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm bg-white px-2 py-1 rounded-full\",\n          children: categories.reduce((total, cat) => total + (serviceCounts[cat] || 0), 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onCategorySelect(category),\n        className: `w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${selectedCategory === category ? 'bg-primary-50 text-primary-700 border-primary-200' : `${getCategoryColor(category)} hover:opacity-80`}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [getCategoryIcon(category), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 font-medium\",\n            children: category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm bg-white px-2 py-1 rounded-full\",\n          children: serviceCounts[category] || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)]\n      }, category, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), categories.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm\",\n        children: \"No categories yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: \"Categories will appear when you add services\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c = ServiceCategories;\nexport default ServiceCategories;\nvar _c;\n$RefreshReg$(_c, \"ServiceCategories\");", "map": {"version": 3, "names": ["React", "Tag", "Monitor", "Printer", "Globe", "FileText", "Building", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ServiceCategories", "categories", "selectedCate<PERSON><PERSON>", "onCategorySelect", "serviceCounts", "getCategoryIcon", "category", "toLowerCase", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getCategoryColor", "children", "onClick", "reduce", "total", "cat", "map", "length", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/services/ServiceCategories.tsx"], "sourcesContent": ["import React from 'react';\nimport { Tag, Monitor, Printer, Globe, FileText, Building, Wrench } from 'lucide-react';\n\ninterface ServiceCategoriesProps {\n  categories: string[];\n  selectedCategory: string;\n  onCategorySelect: (category: string) => void;\n  serviceCounts: Record<string, number>;\n}\n\nconst ServiceCategories: React.FC<ServiceCategoriesProps> = ({\n  categories,\n  selectedCategory,\n  onCategorySelect,\n  serviceCounts\n}) => {\n  const getCategoryIcon = (category: string) => {\n    switch (category.toLowerCase()) {\n      case 'printing':\n        return <Printer className=\"h-4 w-4\" />;\n      case 'scanning':\n        return <Monitor className=\"h-4 w-4\" />;\n      case 'internet':\n        return <Globe className=\"h-4 w-4\" />;\n      case 'typing':\n        return <FileText className=\"h-4 w-4\" />;\n      case 'government':\n        return <Building className=\"h-4 w-4\" />;\n      case 'office':\n        return <Wrench className=\"h-4 w-4\" />;\n      default:\n        return <Tag className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    switch (category.toLowerCase()) {\n      case 'printing':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      case 'scanning':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'internet':\n        return 'bg-purple-100 text-purple-800 border-purple-200';\n      case 'typing':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'government':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'office':\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n      default:\n        return 'bg-indigo-100 text-indigo-800 border-indigo-200';\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-4\">\n      <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n        <Tag className=\"h-5 w-5 mr-2\" />\n        Service Categories\n      </h3>\n      \n      <div className=\"space-y-2\">\n        {/* All Categories */}\n        <button\n          onClick={() => onCategorySelect('')}\n          className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${\n            selectedCategory === ''\n              ? 'bg-primary-50 text-primary-700 border-primary-200'\n              : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'\n          }`}\n        >\n          <div className=\"flex items-center\">\n            <Tag className=\"h-4 w-4 mr-2\" />\n            <span className=\"font-medium\">All Services</span>\n          </div>\n          <span className=\"text-sm bg-white px-2 py-1 rounded-full\">\n            {categories.reduce((total, cat) => total + (serviceCounts[cat] || 0), 0)}\n          </span>\n        </button>\n\n        {/* Individual Categories */}\n        {categories.map((category) => (\n          <button\n            key={category}\n            onClick={() => onCategorySelect(category)}\n            className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${\n              selectedCategory === category\n                ? 'bg-primary-50 text-primary-700 border-primary-200'\n                : `${getCategoryColor(category)} hover:opacity-80`\n            }`}\n          >\n            <div className=\"flex items-center\">\n              {getCategoryIcon(category)}\n              <span className=\"ml-2 font-medium\">{category}</span>\n            </div>\n            <span className=\"text-sm bg-white px-2 py-1 rounded-full\">\n              {serviceCounts[category] || 0}\n            </span>\n          </button>\n        ))}\n      </div>\n\n      {categories.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <Tag className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n          <p className=\"text-sm\">No categories yet</p>\n          <p className=\"text-xs\">Categories will appear when you add services</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ServiceCategories;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASxF,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,UAAU;EACVC,gBAAgB;EAChBC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAIC,QAAgB,IAAK;IAC5C,QAAQA,QAAQ,CAACC,WAAW,CAAC,CAAC;MAC5B,KAAK,UAAU;QACb,oBAAOR,OAAA,CAACN,OAAO;UAACe,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAK,UAAU;QACb,oBAAOb,OAAA,CAACP,OAAO;UAACgB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAK,UAAU;QACb,oBAAOb,OAAA,CAACL,KAAK;UAACc,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,KAAK,QAAQ;QACX,oBAAOb,OAAA,CAACJ,QAAQ;UAACa,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzC,KAAK,YAAY;QACf,oBAAOb,OAAA,CAACH,QAAQ;UAACY,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzC,KAAK,QAAQ;QACX,oBAAOb,OAAA,CAACF,MAAM;UAACW,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvC;QACE,oBAAOb,OAAA,CAACR,GAAG;UAACiB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtC;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIP,QAAgB,IAAK;IAC7C,QAAQA,QAAQ,CAACC,WAAW,CAAC,CAAC;MAC5B,KAAK,UAAU;QACb,OAAO,2CAA2C;MACpD,KAAK,UAAU;QACb,OAAO,8CAA8C;MACvD,KAAK,UAAU;QACb,OAAO,iDAAiD;MAC1D,KAAK,QAAQ;QACX,OAAO,iDAAiD;MAC1D,KAAK,YAAY;QACf,OAAO,wCAAwC;MACjD,KAAK,QAAQ;QACX,OAAO,2CAA2C;MACpD;QACE,OAAO,iDAAiD;IAC5D;EACF,CAAC;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,gCAAgC;IAAAM,QAAA,gBAC7Cf,OAAA;MAAIS,SAAS,EAAC,0DAA0D;MAAAM,QAAA,gBACtEf,OAAA,CAACR,GAAG;QAACiB,SAAS,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,sBAElC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELb,OAAA;MAAKS,SAAS,EAAC,WAAW;MAAAM,QAAA,gBAExBf,OAAA;QACEgB,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAAC,EAAE,CAAE;QACpCK,SAAS,EAAE,oFACTN,gBAAgB,KAAK,EAAE,GACnB,mDAAmD,GACnD,4DAA4D,EAC/D;QAAAY,QAAA,gBAEHf,OAAA;UAAKS,SAAS,EAAC,mBAAmB;UAAAM,QAAA,gBAChCf,OAAA,CAACR,GAAG;YAACiB,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCb,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAM,QAAA,EAAC;UAAY;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNb,OAAA;UAAMS,SAAS,EAAC,yCAAyC;UAAAM,QAAA,EACtDb,UAAU,CAACe,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAKD,KAAK,IAAIb,aAAa,CAACc,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGRX,UAAU,CAACkB,GAAG,CAAEb,QAAQ,iBACvBP,OAAA;QAEEgB,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAACG,QAAQ,CAAE;QAC1CE,SAAS,EAAE,oFACTN,gBAAgB,KAAKI,QAAQ,GACzB,mDAAmD,GACnD,GAAGO,gBAAgB,CAACP,QAAQ,CAAC,mBAAmB,EACnD;QAAAQ,QAAA,gBAEHf,OAAA;UAAKS,SAAS,EAAC,mBAAmB;UAAAM,QAAA,GAC/BT,eAAe,CAACC,QAAQ,CAAC,eAC1BP,OAAA;YAAMS,SAAS,EAAC,kBAAkB;YAAAM,QAAA,EAAER;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNb,OAAA;UAAMS,SAAS,EAAC,yCAAyC;UAAAM,QAAA,EACtDV,aAAa,CAACE,QAAQ,CAAC,IAAI;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA,GAdFN,QAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeP,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELX,UAAU,CAACmB,MAAM,KAAK,CAAC,iBACtBrB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAM,QAAA,gBAC7Cf,OAAA,CAACR,GAAG;QAACiB,SAAS,EAAC;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDb,OAAA;QAAGS,SAAS,EAAC,SAAS;QAAAM,QAAA,EAAC;MAAiB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5Cb,OAAA;QAAGS,SAAS,EAAC,SAAS;QAAAM,QAAA,EAAC;MAA4C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACS,EAAA,GArGIrB,iBAAmD;AAuGzD,eAAeA,iBAAiB;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}