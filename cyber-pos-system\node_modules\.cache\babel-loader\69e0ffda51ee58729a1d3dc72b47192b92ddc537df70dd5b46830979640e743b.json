{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { signInWithEmailAndPassword, signOut, onAuthStateChanged, createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';\nimport { doc, getDoc, setDoc, collection, query, getDocs, updateDoc, serverTimestamp } from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [firebaseUser, setFirebaseUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const login = async (email, password) => {\n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  };\n  const logout = async () => {\n    try {\n      await signOut(auth);\n      setCurrentUser(null);\n      setFirebaseUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n      throw error;\n    }\n  };\n  const createUser = async (email, password, name, role) => {\n    try {\n      // Create Firebase user\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const firebaseUser = userCredential.user;\n\n      // Update display name\n      await updateProfile(firebaseUser, {\n        displayName: name\n      });\n\n      // Create user document in Firestore\n      const userData = {\n        email: firebaseUser.email || '',\n        name,\n        role,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n      await setDoc(doc(db, 'users', firebaseUser.uid), {\n        ...userData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n      console.log('User created successfully');\n    } catch (error) {\n      console.error('Error creating user:', error);\n      throw error;\n    }\n  };\n  const updateUserRole = async (userId, role) => {\n    try {\n      await updateDoc(doc(db, 'users', userId), {\n        role,\n        updatedAt: serverTimestamp()\n      });\n      console.log('User role updated successfully');\n    } catch (error) {\n      console.error('Error updating user role:', error);\n      throw error;\n    }\n  };\n  const getAllUsers = async () => {\n    try {\n      const usersQuery = query(collection(db, 'users'));\n      const querySnapshot = await getDocs(usersQuery);\n      const users = [];\n      querySnapshot.forEach(doc => {\n        var _userData$createdAt, _userData$updatedAt;\n        const userData = doc.data();\n        users.push({\n          id: doc.id,\n          email: userData.email || '',\n          name: userData.name || '',\n          role: userData.role || 'attendant',\n          isActive: userData.isActive !== false,\n          createdAt: ((_userData$createdAt = userData.createdAt) === null || _userData$createdAt === void 0 ? void 0 : _userData$createdAt.toDate()) || new Date(),\n          updatedAt: ((_userData$updatedAt = userData.updatedAt) === null || _userData$updatedAt === void 0 ? void 0 : _userData$updatedAt.toDate()) || new Date()\n        });\n      });\n      return users;\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      throw error;\n    }\n  };\n  const hasPermission = requiredRole => {\n    if (!currentUser) return false;\n    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n\n    // Admin has access to everything\n    if (currentUser.role === 'admin') return true;\n    return roles.includes(currentUser.role);\n  };\n  const fetchUserData = async firebaseUser => {\n    try {\n      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));\n      if (userDoc.exists()) {\n        var _userData$createdAt2, _userData$updatedAt2;\n        const userData = userDoc.data();\n        return {\n          id: firebaseUser.uid,\n          email: firebaseUser.email || '',\n          name: userData.name || '',\n          role: userData.role || 'attendant',\n          isActive: userData.isActive !== false,\n          createdAt: ((_userData$createdAt2 = userData.createdAt) === null || _userData$createdAt2 === void 0 ? void 0 : _userData$createdAt2.toDate()) || new Date(),\n          updatedAt: ((_userData$updatedAt2 = userData.updatedAt) === null || _userData$updatedAt2 === void 0 ? void 0 : _userData$updatedAt2.toDate()) || new Date()\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching user data:', error);\n      return null;\n    }\n  };\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async firebaseUser => {\n      setLoading(true);\n      if (firebaseUser) {\n        setFirebaseUser(firebaseUser);\n        const userData = await fetchUserData(firebaseUser);\n        setCurrentUser(userData);\n      } else {\n        setFirebaseUser(null);\n        setCurrentUser(null);\n      }\n      setLoading(false);\n    });\n    return unsubscribe;\n  }, []);\n  const value = {\n    currentUser,\n    firebaseUser,\n    login,\n    logout,\n    createUser,\n    updateUserRole,\n    getAllUsers,\n    loading,\n    hasPermission\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"vGyLl1g/TTlc+eFqiiFhtUFYIvs=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "signInWithEmailAndPassword", "signOut", "onAuthStateChanged", "createUserWithEmailAndPassword", "updateProfile", "doc", "getDoc", "setDoc", "collection", "query", "getDocs", "updateDoc", "serverTimestamp", "auth", "db", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "currentUser", "setCurrentUser", "firebaseUser", "setFirebaseUser", "loading", "setLoading", "login", "email", "password", "error", "console", "logout", "createUser", "name", "role", "userCredential", "user", "displayName", "userData", "isActive", "createdAt", "Date", "updatedAt", "uid", "log", "updateUserRole", "userId", "getAllUsers", "usersQuery", "querySnapshot", "users", "for<PERSON>ach", "_userData$createdAt", "_userData$updatedAt", "data", "push", "id", "toDate", "hasPermission", "requiredRole", "roles", "Array", "isArray", "includes", "fetchUserData", "userDoc", "exists", "_userData$createdAt2", "_userData$updatedAt2", "unsubscribe", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport {\n  signInWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  User as FirebaseUser,\n  createUserWithEmailAndPassword,\n  updateProfile\n} from 'firebase/auth';\nimport {\n  doc,\n  getDoc,\n  setDoc,\n  collection,\n  query,\n  where,\n  getDocs,\n  updateDoc,\n  serverTimestamp\n} from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { User, UserRole } from '../types';\n\ninterface AuthContextType {\n  currentUser: User | null;\n  firebaseUser: FirebaseUser | null;\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  createUser: (email: string, password: string, name: string, role: UserRole) => Promise<void>;\n  updateUserRole: (userId: string, role: UserRole) => Promise<void>;\n  getAllUsers: () => Promise<User[]>;\n  loading: boolean;\n  hasPermission: (requiredRole: UserRole | UserRole[]) => boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  const login = async (email: string, password: string) => {\n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await signOut(auth);\n      setCurrentUser(null);\n      setFirebaseUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n      throw error;\n    }\n  };\n\n  const createUser = async (email: string, password: string, name: string, role: UserRole) => {\n    try {\n      // Create Firebase user\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const firebaseUser = userCredential.user;\n\n      // Update display name\n      await updateProfile(firebaseUser, { displayName: name });\n\n      // Create user document in Firestore\n      const userData: Omit<User, 'id'> = {\n        email: firebaseUser.email || '',\n        name,\n        role,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      await setDoc(doc(db, 'users', firebaseUser.uid), {\n        ...userData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n\n      console.log('User created successfully');\n    } catch (error) {\n      console.error('Error creating user:', error);\n      throw error;\n    }\n  };\n\n  const updateUserRole = async (userId: string, role: UserRole) => {\n    try {\n      await updateDoc(doc(db, 'users', userId), {\n        role,\n        updatedAt: serverTimestamp(),\n      });\n      console.log('User role updated successfully');\n    } catch (error) {\n      console.error('Error updating user role:', error);\n      throw error;\n    }\n  };\n\n  const getAllUsers = async (): Promise<User[]> => {\n    try {\n      const usersQuery = query(collection(db, 'users'));\n      const querySnapshot = await getDocs(usersQuery);\n\n      const users: User[] = [];\n      querySnapshot.forEach((doc) => {\n        const userData = doc.data();\n        users.push({\n          id: doc.id,\n          email: userData.email || '',\n          name: userData.name || '',\n          role: userData.role || 'attendant',\n          isActive: userData.isActive !== false,\n          createdAt: userData.createdAt?.toDate() || new Date(),\n          updatedAt: userData.updatedAt?.toDate() || new Date(),\n        });\n      });\n\n      return users;\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      throw error;\n    }\n  };\n\n  const hasPermission = (requiredRole: UserRole | UserRole[]): boolean => {\n    if (!currentUser) return false;\n    \n    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n    \n    // Admin has access to everything\n    if (currentUser.role === 'admin') return true;\n    \n    return roles.includes(currentUser.role);\n  };\n\n  const fetchUserData = async (firebaseUser: FirebaseUser): Promise<User | null> => {\n    try {\n      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));\n      if (userDoc.exists()) {\n        const userData = userDoc.data();\n        return {\n          id: firebaseUser.uid,\n          email: firebaseUser.email || '',\n          name: userData.name || '',\n          role: userData.role || 'attendant',\n          isActive: userData.isActive !== false,\n          createdAt: userData.createdAt?.toDate() || new Date(),\n          updatedAt: userData.updatedAt?.toDate() || new Date(),\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching user data:', error);\n      return null;\n    }\n  };\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\n      setLoading(true);\n      \n      if (firebaseUser) {\n        setFirebaseUser(firebaseUser);\n        const userData = await fetchUserData(firebaseUser);\n        setCurrentUser(userData);\n      } else {\n        setFirebaseUser(null);\n        setCurrentUser(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const value: AuthContextType = {\n    currentUser,\n    firebaseUser,\n    login,\n    logout,\n    createUser,\n    updateUserRole,\n    getAllUsers,\n    loading,\n    hasPermission,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,kBAAkB,EAElBC,8BAA8B,EAC9BC,aAAa,QACR,eAAe;AACtB,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EAELC,OAAO,EACPC,SAAS,EACTC,eAAe,QACV,oBAAoB;AAC3B,SAASC,IAAI,EAAEC,EAAE,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe9C,MAAMC,WAAW,gBAAGrB,aAAa,CAA8BsB,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGxB,UAAU,CAACoB,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMiC,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACvD,IAAI;MACF,MAAMlC,0BAA0B,CAACa,IAAI,EAAEoB,KAAK,EAAEC,QAAQ,CAAC;IACzD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAME,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMpC,OAAO,CAACY,IAAI,CAAC;MACnBc,cAAc,CAAC,IAAI,CAAC;MACpBE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAOL,KAAa,EAAEC,QAAgB,EAAEK,IAAY,EAAEC,IAAc,KAAK;IAC1F,IAAI;MACF;MACA,MAAMC,cAAc,GAAG,MAAMtC,8BAA8B,CAACU,IAAI,EAAEoB,KAAK,EAAEC,QAAQ,CAAC;MAClF,MAAMN,YAAY,GAAGa,cAAc,CAACC,IAAI;;MAExC;MACA,MAAMtC,aAAa,CAACwB,YAAY,EAAE;QAAEe,WAAW,EAAEJ;MAAK,CAAC,CAAC;;MAExD;MACA,MAAMK,QAA0B,GAAG;QACjCX,KAAK,EAAEL,YAAY,CAACK,KAAK,IAAI,EAAE;QAC/BM,IAAI;QACJC,IAAI;QACJK,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB,CAAC;MAED,MAAMxC,MAAM,CAACF,GAAG,CAACS,EAAE,EAAE,OAAO,EAAEc,YAAY,CAACqB,GAAG,CAAC,EAAE;QAC/C,GAAGL,QAAQ;QACXE,SAAS,EAAElC,eAAe,CAAC,CAAC;QAC5BoC,SAAS,EAAEpC,eAAe,CAAC;MAC7B,CAAC,CAAC;MAEFwB,OAAO,CAACc,GAAG,CAAC,2BAA2B,CAAC;IAC1C,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMgB,cAAc,GAAG,MAAAA,CAAOC,MAAc,EAAEZ,IAAc,KAAK;IAC/D,IAAI;MACF,MAAM7B,SAAS,CAACN,GAAG,CAACS,EAAE,EAAE,OAAO,EAAEsC,MAAM,CAAC,EAAE;QACxCZ,IAAI;QACJQ,SAAS,EAAEpC,eAAe,CAAC;MAC7B,CAAC,CAAC;MACFwB,OAAO,CAACc,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMkB,WAAW,GAAG,MAAAA,CAAA,KAA6B;IAC/C,IAAI;MACF,MAAMC,UAAU,GAAG7C,KAAK,CAACD,UAAU,CAACM,EAAE,EAAE,OAAO,CAAC,CAAC;MACjD,MAAMyC,aAAa,GAAG,MAAM7C,OAAO,CAAC4C,UAAU,CAAC;MAE/C,MAAME,KAAa,GAAG,EAAE;MACxBD,aAAa,CAACE,OAAO,CAAEpD,GAAG,IAAK;QAAA,IAAAqD,mBAAA,EAAAC,mBAAA;QAC7B,MAAMf,QAAQ,GAAGvC,GAAG,CAACuD,IAAI,CAAC,CAAC;QAC3BJ,KAAK,CAACK,IAAI,CAAC;UACTC,EAAE,EAAEzD,GAAG,CAACyD,EAAE;UACV7B,KAAK,EAAEW,QAAQ,CAACX,KAAK,IAAI,EAAE;UAC3BM,IAAI,EAAEK,QAAQ,CAACL,IAAI,IAAI,EAAE;UACzBC,IAAI,EAAEI,QAAQ,CAACJ,IAAI,IAAI,WAAW;UAClCK,QAAQ,EAAED,QAAQ,CAACC,QAAQ,KAAK,KAAK;UACrCC,SAAS,EAAE,EAAAY,mBAAA,GAAAd,QAAQ,CAACE,SAAS,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBK,MAAM,CAAC,CAAC,KAAI,IAAIhB,IAAI,CAAC,CAAC;UACrDC,SAAS,EAAE,EAAAW,mBAAA,GAAAf,QAAQ,CAACI,SAAS,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBI,MAAM,CAAC,CAAC,KAAI,IAAIhB,IAAI,CAAC;QACtD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAOS,KAAK;IACd,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM6B,aAAa,GAAIC,YAAmC,IAAc;IACtE,IAAI,CAACvC,WAAW,EAAE,OAAO,KAAK;IAE9B,MAAMwC,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;;IAEzE;IACA,IAAIvC,WAAW,CAACc,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAE7C,OAAO0B,KAAK,CAACG,QAAQ,CAAC3C,WAAW,CAACc,IAAI,CAAC;EACzC,CAAC;EAED,MAAM8B,aAAa,GAAG,MAAO1C,YAA0B,IAA2B;IAChF,IAAI;MACF,MAAM2C,OAAO,GAAG,MAAMjE,MAAM,CAACD,GAAG,CAACS,EAAE,EAAE,OAAO,EAAEc,YAAY,CAACqB,GAAG,CAAC,CAAC;MAChE,IAAIsB,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;QAAA,IAAAC,oBAAA,EAAAC,oBAAA;QACpB,MAAM9B,QAAQ,GAAG2B,OAAO,CAACX,IAAI,CAAC,CAAC;QAC/B,OAAO;UACLE,EAAE,EAAElC,YAAY,CAACqB,GAAG;UACpBhB,KAAK,EAAEL,YAAY,CAACK,KAAK,IAAI,EAAE;UAC/BM,IAAI,EAAEK,QAAQ,CAACL,IAAI,IAAI,EAAE;UACzBC,IAAI,EAAEI,QAAQ,CAACJ,IAAI,IAAI,WAAW;UAClCK,QAAQ,EAAED,QAAQ,CAACC,QAAQ,KAAK,KAAK;UACrCC,SAAS,EAAE,EAAA2B,oBAAA,GAAA7B,QAAQ,CAACE,SAAS,cAAA2B,oBAAA,uBAAlBA,oBAAA,CAAoBV,MAAM,CAAC,CAAC,KAAI,IAAIhB,IAAI,CAAC,CAAC;UACrDC,SAAS,EAAE,EAAA0B,oBAAA,GAAA9B,QAAQ,CAACI,SAAS,cAAA0B,oBAAA,uBAAlBA,oBAAA,CAAoBX,MAAM,CAAC,CAAC,KAAI,IAAIhB,IAAI,CAAC;QACtD,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,IAAI;IACb;EACF,CAAC;EAEDrC,SAAS,CAAC,MAAM;IACd,MAAM6E,WAAW,GAAGzE,kBAAkB,CAACW,IAAI,EAAE,MAAOe,YAAY,IAAK;MACnEG,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIH,YAAY,EAAE;QAChBC,eAAe,CAACD,YAAY,CAAC;QAC7B,MAAMgB,QAAQ,GAAG,MAAM0B,aAAa,CAAC1C,YAAY,CAAC;QAClDD,cAAc,CAACiB,QAAQ,CAAC;MAC1B,CAAC,MAAM;QACLf,eAAe,CAAC,IAAI,CAAC;QACrBF,cAAc,CAAC,IAAI,CAAC;MACtB;MAEAI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO4C,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAsB,GAAG;IAC7BlD,WAAW;IACXE,YAAY;IACZI,KAAK;IACLK,MAAM;IACNC,UAAU;IACVa,cAAc;IACdE,WAAW;IACXvB,OAAO;IACPkC;EACF,CAAC;EAED,oBACEhD,OAAA,CAACC,WAAW,CAAC4D,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAApD,QAAA,EAChCA;EAAQ;IAAAsD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACxD,GAAA,CArKWF,YAAyC;AAAA2D,EAAA,GAAzC3D,YAAyC;AAAA,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}