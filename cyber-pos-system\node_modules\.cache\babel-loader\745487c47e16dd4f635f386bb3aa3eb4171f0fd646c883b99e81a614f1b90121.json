{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"16\",\n  rx: \"2\",\n  key: \"1i8z2d\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"13squh\"\n}], [\"path\", {\n  d: \"M2 2h20\",\n  key: \"1ennik\"\n}]];\nconst AlignVerticalJustifyStart = createLucideIcon(\"align-vertical-justify-start\", __iconNode);\nexport { __iconNode, AlignVerticalJustifyStart as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "AlignVerticalJustifyStart", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\align-vertical-justify-start.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '14', height: '6', x: '5', y: '16', rx: '2', key: '1i8z2d' }],\n  ['rect', { width: '10', height: '6', x: '7', y: '6', rx: '2', key: '13squh' }],\n  ['path', { d: 'M2 2h20', key: '1ennik' }],\n];\n\n/**\n * @component @name AlignVerticalJustifyStart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iNiIgeD0iNSIgeT0iMTYiIHJ4PSIyIiAvPgogIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSI2IiB4PSI3IiB5PSI2IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMiAyaDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-vertical-justify-start\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignVerticalJustifyStart = createLucideIcon('align-vertical-justify-start', __iconNode);\n\nexport default AlignVerticalJustifyStart;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAE,yBAAA,GAA4BC,gBAAiB,iCAAgCT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}