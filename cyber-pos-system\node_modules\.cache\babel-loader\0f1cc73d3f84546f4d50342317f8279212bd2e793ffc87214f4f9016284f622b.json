{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nexport const useCart = (allServices = []) => {\n  _s();\n  const [cartItems, setCartItems] = useState([]);\n  const [discount, setDiscount] = useState(0);\n\n  // Calculate cart totals\n  const calculateTotals = useCallback((items, discountAmount = 0) => {\n    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);\n\n    // Calculate bundled services value\n    const bundledValue = items.reduce((sum, item) => {\n      if (item.type === 'service') {\n        var _service$bundledServi;\n        const service = allServices.find(s => s.id === item.itemId);\n        if (service !== null && service !== void 0 && (_service$bundledServi = service.bundledServices) !== null && _service$bundledServi !== void 0 && _service$bundledServi.length) {\n          const bundledServices = service.bundledServices.map(id => allServices.find(s => s.id === id)).filter(Boolean);\n          return sum + bundledServices.reduce((bundleSum, bundledService) => bundleSum + bundledService.basePrice, 0);\n        }\n      }\n      return sum;\n    }, 0);\n    const total = Math.max(0, subtotal - discountAmount);\n    return {\n      items,\n      subtotal,\n      discount: discountAmount,\n      total,\n      bundledValue\n    };\n  }, [allServices]);\n\n  // Add item to cart\n  const addToCart = useCallback((item, type, quantity = 1, customPrice, notes) => {\n    const itemId = item.id;\n    const unitPrice = customPrice !== null && customPrice !== void 0 ? customPrice : type === 'service' ? item.basePrice : item.price;\n    const totalPrice = unitPrice * quantity;\n    const cartItem = {\n      id: `${type}-${itemId}-${Date.now()}`,\n      type,\n      itemId,\n      name: item.name,\n      quantity,\n      unitPrice,\n      originalPrice: type === 'service' ? item.basePrice : item.price,\n      totalPrice,\n      notes\n    };\n    setCartItems(prev => [...prev, cartItem]);\n  }, []);\n\n  // Update item quantity\n  const updateQuantity = useCallback((cartItemId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(cartItemId);\n      return;\n    }\n    setCartItems(prev => prev.map(item => item.id === cartItemId ? {\n      ...item,\n      quantity,\n      totalPrice: item.unitPrice * quantity\n    } : item));\n  }, []);\n\n  // Update item price\n  const updatePrice = useCallback((cartItemId, newPrice) => {\n    setCartItems(prev => prev.map(item => item.id === cartItemId ? {\n      ...item,\n      unitPrice: newPrice,\n      totalPrice: newPrice * item.quantity\n    } : item));\n  }, []);\n\n  // Update item notes\n  const updateNotes = useCallback((cartItemId, notes) => {\n    setCartItems(prev => prev.map(item => item.id === cartItemId ? {\n      ...item,\n      notes\n    } : item));\n  }, []);\n\n  // Remove item from cart\n  const removeFromCart = useCallback(cartItemId => {\n    setCartItems(prev => prev.filter(item => item.id !== cartItemId));\n  }, []);\n\n  // Clear entire cart\n  const clearCart = useCallback(() => {\n    setCartItems([]);\n    setDiscount(0);\n  }, []);\n\n  // Apply discount\n  const applyDiscount = useCallback(discountAmount => {\n    setDiscount(Math.max(0, discountAmount));\n  }, []);\n\n  // Get cart state\n  const cartState = calculateTotals(cartItems, discount);\n\n  // Check if item exists in cart\n  const isInCart = useCallback((itemId, type) => {\n    return cartItems.some(item => item.itemId === itemId && item.type === type);\n  }, [cartItems]);\n\n  // Get item quantity in cart\n  const getItemQuantity = useCallback((itemId, type) => {\n    const item = cartItems.find(item => item.itemId === itemId && item.type === type);\n    return (item === null || item === void 0 ? void 0 : item.quantity) || 0;\n  }, [cartItems]);\n\n  // Get bundled services for cart items\n  const getBundledServices = useCallback(() => {\n    const bundledServices = [];\n    cartItems.forEach(item => {\n      if (item.type === 'service') {\n        var _service$bundledServi2;\n        const service = allServices.find(s => s.id === item.itemId);\n        if (service !== null && service !== void 0 && (_service$bundledServi2 = service.bundledServices) !== null && _service$bundledServi2 !== void 0 && _service$bundledServi2.length) {\n          service.bundledServices.forEach(bundledId => {\n            const bundledService = allServices.find(s => s.id === bundledId);\n            if (bundledService) {\n              bundledServices.push({\n                service: bundledService,\n                fromService: service\n              });\n            }\n          });\n        }\n      }\n    });\n    return bundledServices;\n  }, [cartItems, allServices]);\n  return {\n    cartState,\n    addToCart,\n    updateQuantity,\n    updatePrice,\n    updateNotes,\n    removeFromCart,\n    clearCart,\n    applyDiscount,\n    isInCart,\n    getItemQuantity,\n    getBundledServices,\n    itemCount: cartItems.length,\n    isEmpty: cartItems.length === 0\n  };\n};\n_s(useCart, \"tAbW7U2ZL4T82ZmQRIhadxogF08=\");", "map": {"version": 3, "names": ["useState", "useCallback", "useCart", "allServices", "_s", "cartItems", "setCartItems", "discount", "setDiscount", "calculateTotals", "items", "discountAmount", "subtotal", "reduce", "sum", "item", "totalPrice", "bundledValue", "type", "_service$bundledServi", "service", "find", "s", "id", "itemId", "bundledServices", "length", "map", "filter", "Boolean", "bundleSum", "bundledService", "basePrice", "total", "Math", "max", "addToCart", "quantity", "customPrice", "notes", "unitPrice", "price", "cartItem", "Date", "now", "name", "originalPrice", "prev", "updateQuantity", "cartItemId", "removeFromCart", "updatePrice", "newPrice", "updateNotes", "clearCart", "applyDiscount", "cartState", "isInCart", "some", "getItemQuantity", "getBundledServices", "for<PERSON>ach", "_service$bundledServi2", "bundledId", "push", "fromService", "itemCount", "isEmpty"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useCart.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { CartItem, Service, Product } from '../types';\nimport { calculateServicePrice } from '../utils/serviceUtils';\n\nexport interface CartState {\n  items: CartItem[];\n  subtotal: number;\n  discount: number;\n  total: number;\n  bundledValue: number;\n}\n\nexport const useCart = (allServices: Service[] = []) => {\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n  const [discount, setDiscount] = useState(0);\n\n  // Calculate cart totals\n  const calculateTotals = useCallback((items: CartItem[], discountAmount: number = 0): CartState => {\n    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);\n    \n    // Calculate bundled services value\n    const bundledValue = items.reduce((sum, item) => {\n      if (item.type === 'service') {\n        const service = allServices.find(s => s.id === item.itemId);\n        if (service?.bundledServices?.length) {\n          const bundledServices = service.bundledServices\n            .map(id => allServices.find(s => s.id === id))\n            .filter(Boolean) as Service[];\n          return sum + bundledServices.reduce((bundleSum, bundledService) => \n            bundleSum + bundledService.basePrice, 0\n          );\n        }\n      }\n      return sum;\n    }, 0);\n\n    const total = Math.max(0, subtotal - discountAmount);\n\n    return {\n      items,\n      subtotal,\n      discount: discountAmount,\n      total,\n      bundledValue\n    };\n  }, [allServices]);\n\n  // Add item to cart\n  const addToCart = useCallback((\n    item: Service | Product,\n    type: 'service' | 'product',\n    quantity: number = 1,\n    customPrice?: number,\n    notes?: string\n  ) => {\n    const itemId = item.id;\n    const unitPrice = customPrice ?? (type === 'service' ? (item as Service).basePrice : (item as Product).price);\n    const totalPrice = unitPrice * quantity;\n\n    const cartItem: CartItem = {\n      id: `${type}-${itemId}-${Date.now()}`,\n      type,\n      itemId,\n      name: item.name,\n      quantity,\n      unitPrice,\n      originalPrice: type === 'service' ? (item as Service).basePrice : (item as Product).price,\n      totalPrice,\n      notes\n    };\n\n    setCartItems(prev => [...prev, cartItem]);\n  }, []);\n\n  // Update item quantity\n  const updateQuantity = useCallback((cartItemId: string, quantity: number) => {\n    if (quantity <= 0) {\n      removeFromCart(cartItemId);\n      return;\n    }\n\n    setCartItems(prev => prev.map(item => \n      item.id === cartItemId \n        ? { ...item, quantity, totalPrice: item.unitPrice * quantity }\n        : item\n    ));\n  }, []);\n\n  // Update item price\n  const updatePrice = useCallback((cartItemId: string, newPrice: number) => {\n    setCartItems(prev => prev.map(item => \n      item.id === cartItemId \n        ? { ...item, unitPrice: newPrice, totalPrice: newPrice * item.quantity }\n        : item\n    ));\n  }, []);\n\n  // Update item notes\n  const updateNotes = useCallback((cartItemId: string, notes: string) => {\n    setCartItems(prev => prev.map(item => \n      item.id === cartItemId \n        ? { ...item, notes }\n        : item\n    ));\n  }, []);\n\n  // Remove item from cart\n  const removeFromCart = useCallback((cartItemId: string) => {\n    setCartItems(prev => prev.filter(item => item.id !== cartItemId));\n  }, []);\n\n  // Clear entire cart\n  const clearCart = useCallback(() => {\n    setCartItems([]);\n    setDiscount(0);\n  }, []);\n\n  // Apply discount\n  const applyDiscount = useCallback((discountAmount: number) => {\n    setDiscount(Math.max(0, discountAmount));\n  }, []);\n\n  // Get cart state\n  const cartState = calculateTotals(cartItems, discount);\n\n  // Check if item exists in cart\n  const isInCart = useCallback((itemId: string, type: 'service' | 'product') => {\n    return cartItems.some(item => item.itemId === itemId && item.type === type);\n  }, [cartItems]);\n\n  // Get item quantity in cart\n  const getItemQuantity = useCallback((itemId: string, type: 'service' | 'product') => {\n    const item = cartItems.find(item => item.itemId === itemId && item.type === type);\n    return item?.quantity || 0;\n  }, [cartItems]);\n\n  // Get bundled services for cart items\n  const getBundledServices = useCallback(() => {\n    const bundledServices: Array<{ service: Service; fromService: Service }> = [];\n    \n    cartItems.forEach(item => {\n      if (item.type === 'service') {\n        const service = allServices.find(s => s.id === item.itemId);\n        if (service?.bundledServices?.length) {\n          service.bundledServices.forEach(bundledId => {\n            const bundledService = allServices.find(s => s.id === bundledId);\n            if (bundledService) {\n              bundledServices.push({ service: bundledService, fromService: service });\n            }\n          });\n        }\n      }\n    });\n\n    return bundledServices;\n  }, [cartItems, allServices]);\n\n  return {\n    cartState,\n    addToCart,\n    updateQuantity,\n    updatePrice,\n    updateNotes,\n    removeFromCart,\n    clearCart,\n    applyDiscount,\n    isInCart,\n    getItemQuantity,\n    getBundledServices,\n    itemCount: cartItems.length,\n    isEmpty: cartItems.length === 0\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAY7C,OAAO,MAAMC,OAAO,GAAGA,CAACC,WAAsB,GAAG,EAAE,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGN,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAMS,eAAe,GAAGR,WAAW,CAAC,CAACS,KAAiB,EAAEC,cAAsB,GAAG,CAAC,KAAgB;IAChG,MAAMC,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,UAAU,EAAE,CAAC,CAAC;;IAEtE;IACA,MAAMC,YAAY,GAAGP,KAAK,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAC/C,IAAIA,IAAI,CAACG,IAAI,KAAK,SAAS,EAAE;QAAA,IAAAC,qBAAA;QAC3B,MAAMC,OAAO,GAAGjB,WAAW,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,IAAI,CAACS,MAAM,CAAC;QAC3D,IAAIJ,OAAO,aAAPA,OAAO,gBAAAD,qBAAA,GAAPC,OAAO,CAAEK,eAAe,cAAAN,qBAAA,eAAxBA,qBAAA,CAA0BO,MAAM,EAAE;UACpC,MAAMD,eAAe,GAAGL,OAAO,CAACK,eAAe,CAC5CE,GAAG,CAACJ,EAAE,IAAIpB,WAAW,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKA,EAAE,CAAC,CAAC,CAC7CK,MAAM,CAACC,OAAO,CAAc;UAC/B,OAAOf,GAAG,GAAGW,eAAe,CAACZ,MAAM,CAAC,CAACiB,SAAS,EAAEC,cAAc,KAC5DD,SAAS,GAAGC,cAAc,CAACC,SAAS,EAAE,CACxC,CAAC;QACH;MACF;MACA,OAAOlB,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC;IAEL,MAAMmB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEvB,QAAQ,GAAGD,cAAc,CAAC;IAEpD,OAAO;MACLD,KAAK;MACLE,QAAQ;MACRL,QAAQ,EAAEI,cAAc;MACxBsB,KAAK;MACLhB;IACF,CAAC;EACH,CAAC,EAAE,CAACd,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,SAAS,GAAGnC,WAAW,CAAC,CAC5Bc,IAAuB,EACvBG,IAA2B,EAC3BmB,QAAgB,GAAG,CAAC,EACpBC,WAAoB,EACpBC,KAAc,KACX;IACH,MAAMf,MAAM,GAAGT,IAAI,CAACQ,EAAE;IACtB,MAAMiB,SAAS,GAAGF,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAKpB,IAAI,KAAK,SAAS,GAAIH,IAAI,CAAaiB,SAAS,GAAIjB,IAAI,CAAa0B,KAAM;IAC7G,MAAMzB,UAAU,GAAGwB,SAAS,GAAGH,QAAQ;IAEvC,MAAMK,QAAkB,GAAG;MACzBnB,EAAE,EAAE,GAAGL,IAAI,IAAIM,MAAM,IAAImB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACrC1B,IAAI;MACJM,MAAM;MACNqB,IAAI,EAAE9B,IAAI,CAAC8B,IAAI;MACfR,QAAQ;MACRG,SAAS;MACTM,aAAa,EAAE5B,IAAI,KAAK,SAAS,GAAIH,IAAI,CAAaiB,SAAS,GAAIjB,IAAI,CAAa0B,KAAK;MACzFzB,UAAU;MACVuB;IACF,CAAC;IAEDjC,YAAY,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEL,QAAQ,CAAC,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,cAAc,GAAG/C,WAAW,CAAC,CAACgD,UAAkB,EAAEZ,QAAgB,KAAK;IAC3E,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBa,cAAc,CAACD,UAAU,CAAC;MAC1B;IACF;IAEA3C,YAAY,CAACyC,IAAI,IAAIA,IAAI,CAACpB,GAAG,CAACZ,IAAI,IAChCA,IAAI,CAACQ,EAAE,KAAK0B,UAAU,GAClB;MAAE,GAAGlC,IAAI;MAAEsB,QAAQ;MAAErB,UAAU,EAAED,IAAI,CAACyB,SAAS,GAAGH;IAAS,CAAC,GAC5DtB,IACN,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoC,WAAW,GAAGlD,WAAW,CAAC,CAACgD,UAAkB,EAAEG,QAAgB,KAAK;IACxE9C,YAAY,CAACyC,IAAI,IAAIA,IAAI,CAACpB,GAAG,CAACZ,IAAI,IAChCA,IAAI,CAACQ,EAAE,KAAK0B,UAAU,GAClB;MAAE,GAAGlC,IAAI;MAAEyB,SAAS,EAAEY,QAAQ;MAAEpC,UAAU,EAAEoC,QAAQ,GAAGrC,IAAI,CAACsB;IAAS,CAAC,GACtEtB,IACN,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsC,WAAW,GAAGpD,WAAW,CAAC,CAACgD,UAAkB,EAAEV,KAAa,KAAK;IACrEjC,YAAY,CAACyC,IAAI,IAAIA,IAAI,CAACpB,GAAG,CAACZ,IAAI,IAChCA,IAAI,CAACQ,EAAE,KAAK0B,UAAU,GAClB;MAAE,GAAGlC,IAAI;MAAEwB;IAAM,CAAC,GAClBxB,IACN,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,cAAc,GAAGjD,WAAW,CAAEgD,UAAkB,IAAK;IACzD3C,YAAY,CAACyC,IAAI,IAAIA,IAAI,CAACnB,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACQ,EAAE,KAAK0B,UAAU,CAAC,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGrD,WAAW,CAAC,MAAM;IAClCK,YAAY,CAAC,EAAE,CAAC;IAChBE,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+C,aAAa,GAAGtD,WAAW,CAAEU,cAAsB,IAAK;IAC5DH,WAAW,CAAC0B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExB,cAAc,CAAC,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6C,SAAS,GAAG/C,eAAe,CAACJ,SAAS,EAAEE,QAAQ,CAAC;;EAEtD;EACA,MAAMkD,QAAQ,GAAGxD,WAAW,CAAC,CAACuB,MAAc,EAAEN,IAA2B,KAAK;IAC5E,OAAOb,SAAS,CAACqD,IAAI,CAAC3C,IAAI,IAAIA,IAAI,CAACS,MAAM,KAAKA,MAAM,IAAIT,IAAI,CAACG,IAAI,KAAKA,IAAI,CAAC;EAC7E,CAAC,EAAE,CAACb,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMsD,eAAe,GAAG1D,WAAW,CAAC,CAACuB,MAAc,EAAEN,IAA2B,KAAK;IACnF,MAAMH,IAAI,GAAGV,SAAS,CAACgB,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACS,MAAM,KAAKA,MAAM,IAAIT,IAAI,CAACG,IAAI,KAAKA,IAAI,CAAC;IACjF,OAAO,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,KAAI,CAAC;EAC5B,CAAC,EAAE,CAAChC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMuD,kBAAkB,GAAG3D,WAAW,CAAC,MAAM;IAC3C,MAAMwB,eAAkE,GAAG,EAAE;IAE7EpB,SAAS,CAACwD,OAAO,CAAC9C,IAAI,IAAI;MACxB,IAAIA,IAAI,CAACG,IAAI,KAAK,SAAS,EAAE;QAAA,IAAA4C,sBAAA;QAC3B,MAAM1C,OAAO,GAAGjB,WAAW,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,IAAI,CAACS,MAAM,CAAC;QAC3D,IAAIJ,OAAO,aAAPA,OAAO,gBAAA0C,sBAAA,GAAP1C,OAAO,CAAEK,eAAe,cAAAqC,sBAAA,eAAxBA,sBAAA,CAA0BpC,MAAM,EAAE;UACpCN,OAAO,CAACK,eAAe,CAACoC,OAAO,CAACE,SAAS,IAAI;YAC3C,MAAMhC,cAAc,GAAG5B,WAAW,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKwC,SAAS,CAAC;YAChE,IAAIhC,cAAc,EAAE;cAClBN,eAAe,CAACuC,IAAI,CAAC;gBAAE5C,OAAO,EAAEW,cAAc;gBAAEkC,WAAW,EAAE7C;cAAQ,CAAC,CAAC;YACzE;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IAEF,OAAOK,eAAe;EACxB,CAAC,EAAE,CAACpB,SAAS,EAAEF,WAAW,CAAC,CAAC;EAE5B,OAAO;IACLqD,SAAS;IACTpB,SAAS;IACTY,cAAc;IACdG,WAAW;IACXE,WAAW;IACXH,cAAc;IACdI,SAAS;IACTC,aAAa;IACbE,QAAQ;IACRE,eAAe;IACfC,kBAAkB;IAClBM,SAAS,EAAE7D,SAAS,CAACqB,MAAM;IAC3ByC,OAAO,EAAE9D,SAAS,CAACqB,MAAM,KAAK;EAChC,CAAC;AACH,CAAC;AAACtB,EAAA,CAhKWF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}