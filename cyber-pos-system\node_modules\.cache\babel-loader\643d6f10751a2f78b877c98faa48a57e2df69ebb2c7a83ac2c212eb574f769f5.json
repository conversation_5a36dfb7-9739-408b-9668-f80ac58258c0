{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.228 21.925A10 10 0 1 1 21.994 12.338\",\n  key: \"1fzlyi\"\n}], [\"path\", {\n  d: \"M12 6v6l1.562.781\",\n  key: \"1ujuk9\"\n}], [\"path\", {\n  d: \"m14 18 4-4 4 4\",\n  key: \"ftkppy\"\n}], [\"path\", {\n  d: \"M18 22v-8\",\n  key: \"su0gjh\"\n}]];\nconst ClockArrowUp = createLucideIcon(\"clock-arrow-up\", __iconNode);\nexport { __iconNode, ClockArrowUp as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ClockArrowUp", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\clock-arrow-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.228 21.925A10 10 0 1 1 21.994 12.338', key: '1f<PERSON><PERSON><PERSON>' }],\n  ['path', { d: 'M12 6v6l1.562.781', key: '1ujuk9' }],\n  ['path', { d: 'm14 18 4-4 4 4', key: 'ftkppy' }],\n  ['path', { d: 'M18 22v-8', key: 'su0gjh' }],\n];\n\n/**\n * @component @name ClockArrowUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuMjI4IDIxLjkyNUExMCAxMCAwIDEgMSAyMS45OTQgMTIuMzM4IiAvPgogIDxwYXRoIGQ9Ik0xMiA2djZsMS41NjIuNzgxIiAvPgogIDxwYXRoIGQ9Im0xNCAxOCA0LTQgNCA0IiAvPgogIDxwYXRoIGQ9Ik0xOCAyMnYtOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock-arrow-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ClockArrowUp = createLucideIcon('clock-arrow-up', __iconNode);\n\nexport default ClockArrowUp;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAC,YAAA,GAAeC,gBAAiB,mBAAkBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}