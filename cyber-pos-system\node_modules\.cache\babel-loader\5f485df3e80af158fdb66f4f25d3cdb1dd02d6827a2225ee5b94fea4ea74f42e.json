{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.338 21.994A10 10 0 1 1 21.925 13.227\",\n  key: \"1i7shu\"\n}], [\"path\", {\n  d: \"M12 6v6l2 1\",\n  key: \"19cm8n\"\n}], [\"path\", {\n  d: \"m14 18 4 4 4-4\",\n  key: \"1waygx\"\n}], [\"path\", {\n  d: \"M18 14v8\",\n  key: \"irew45\"\n}]];\nconst ClockArrowDown = createLucideIcon(\"clock-arrow-down\", __iconNode);\nexport { __iconNode, ClockArrowDown as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ClockArrowDown", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\clock-arrow-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12.338 21.994A10 10 0 1 1 21.925 13.227', key: '1i7shu' }],\n  ['path', { d: 'M12 6v6l2 1', key: '19cm8n' }],\n  ['path', { d: 'm14 18 4 4 4-4', key: '1waygx' }],\n  ['path', { d: 'M18 14v8', key: 'irew45' }],\n];\n\n/**\n * @component @name ClockArrowDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMzM4IDIxLjk5NEExMCAxMCAwIDEgMSAyMS45MjUgMTMuMjI3IiAvPgogIDxwYXRoIGQ9Ik0xMiA2djZsMiAxIiAvPgogIDxwYXRoIGQ9Im0xNCAxOCA0IDQgNC00IiAvPgogIDxwYXRoIGQ9Ik0xOCAxNHY4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock-arrow-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ClockArrowDown = createLucideIcon('clock-arrow-down', __iconNode);\n\nexport default ClockArrowDown;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,qBAAoBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}