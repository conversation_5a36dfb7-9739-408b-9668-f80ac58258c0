{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.17 7.83 2 22\",\n  key: \"t58vo8\"\n}], [\"path\", {\n  d: \"M4.02 12a2.827 2.827 0 1 1 3.81-4.17A2.827 2.827 0 1 1 12 4.02a2.827 2.827 0 1 1 4.17 3.81A2.827 2.827 0 1 1 19.98 12a2.827 2.827 0 1 1-3.81 4.17A2.827 2.827 0 1 1 12 19.98a2.827 2.827 0 1 1-4.17-3.81A1 1 0 1 1 4 12\",\n  key: \"17k36q\"\n}], [\"path\", {\n  d: \"m7.83 7.83 8.34 8.34\",\n  key: \"1d7sxk\"\n}]];\nconst Clover = createLucideIcon(\"clover\", __iconNode);\nexport { __iconNode, Clover as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Clover", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\clover.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16.17 7.83 2 22', key: 't58vo8' }],\n  [\n    'path',\n    {\n      d: 'M4.02 12a2.827 2.827 0 1 1 3.81-4.17A2.827 2.827 0 1 1 12 4.02a2.827 2.827 0 1 1 4.17 3.81A2.827 2.827 0 1 1 19.98 12a2.827 2.827 0 1 1-3.81 4.17A2.827 2.827 0 1 1 12 19.98a2.827 2.827 0 1 1-4.17-3.81A1 1 0 1 1 4 12',\n      key: '17k36q',\n    },\n  ],\n  ['path', { d: 'm7.83 7.83 8.34 8.34', key: '1d7sxk' }],\n];\n\n/**\n * @component @name Clover\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYuMTcgNy44MyAyIDIyIiAvPgogIDxwYXRoIGQ9Ik00LjAyIDEyYTIuODI3IDIuODI3IDAgMSAxIDMuODEtNC4xN0EyLjgyNyAyLjgyNyAwIDEgMSAxMiA0LjAyYTIuODI3IDIuODI3IDAgMSAxIDQuMTcgMy44MUEyLjgyNyAyLjgyNyAwIDEgMSAxOS45OCAxMmEyLjgyNyAyLjgyNyAwIDEgMS0zLjgxIDQuMTdBMi44MjcgMi44MjcgMCAxIDEgMTIgMTkuOThhMi44MjcgMi44MjcgMCAxIDEtNC4xNy0zLjgxQTEgMSAwIDEgMSA0IDEyIiAvPgogIDxwYXRoIGQ9Im03LjgzIDcuODMgOC4zNCA4LjM0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clover\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clover = createLucideIcon('clover', __iconNode);\n\nexport default Clover;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAU,GACvD;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}