import React, { createContext, useContext } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import { User, UserRole } from '../types';

// Create a mock AuthContext for testing
interface MockAuthContextType {
  currentUser: User | null;
  firebaseUser: any | null;
  login: (...args: any[]) => any;
  logout: (...args: any[]) => any;
  createUser: (...args: any[]) => any;
  updateUserRole: (...args: any[]) => any;
  getAllUsers: (...args: any[]) => any;
  loading: boolean;
  hasPermission: (...args: any[]) => any;
}

const MockAuthContext = createContext<MockAuthContextType | undefined>(undefined);

// Mock Firebase modules for testing
// Simple mock function for development
const mockFn = (implementation?: (...args: any[]) => any) => {
  const fn = implementation || (() => {});
  return Object.assign(fn, {
    mockReturnValue: (value: any) => fn,
    mockImplementation: (impl: any) => Object.assign(fn, impl),
  });
};

export const mockFirebaseConfig = {
  auth: {
    onAuthStateChanged: mockFn((callback: any) => {
      callback(null);
      return mockFn();
    }),
  },
  db: {},
  storage: {},
};

// Mock user data for testing
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'attendant' as UserRole,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Mock Firebase Auth user
export const createMockFirebaseUser = (overrides = {}) => ({
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  emailVerified: true,
  ...overrides,
});

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  user?: User | null;
}

// Mock AuthProvider component for testing
const MockAuthProvider: React.FC<{ children: React.ReactNode; value: MockAuthContextType }> = ({ children, value }) => (
  <MockAuthContext.Provider value={value}>
    {children}
  </MockAuthContext.Provider>
);

// Hook to use mock auth context in tests
export const useMockAuth = () => {
  const context = useContext(MockAuthContext);
  if (context === undefined) {
    throw new Error('useMockAuth must be used within a MockAuthProvider');
  }
  return context;
};

export const renderWithProviders = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries = ['/'], user = null, ...renderOptions } = options;

  // Mock AuthContext value
  const mockAuthContextValue: MockAuthContextType = {
    currentUser: user,
    firebaseUser: user ? createMockFirebaseUser() : null,
    login: mockFn(),
    logout: mockFn(),
    createUser: mockFn(),
    updateUserRole: mockFn(),
    getAllUsers: mockFn(),
    loading: false,
    hasPermission: mockFn((requiredRole: any) => {
      if (!user) return false;
      if (Array.isArray(requiredRole)) {
        return requiredRole.includes(user.role);
      }
      return user.role === requiredRole || user.role === 'admin';
    }),
  };

  // Create wrapper component
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <BrowserRouter>
      <MockAuthProvider value={mockAuthContextValue}>
        {children}
      </MockAuthProvider>
    </BrowserRouter>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock hooks for testing
export const mockUseServices = {
  services: [],
  loading: false,
  error: null,
  addService: mockFn(),
  updateService: mockFn(),
  deleteService: mockFn(),
  getServiceById: mockFn(),
};

export const mockUseProducts = {
  products: [],
  loading: false,
  error: null,
  addProduct: mockFn(),
  updateProduct: mockFn(),
  deleteProduct: mockFn(),
  getProductById: mockFn(),
  adjustStock: mockFn(),
};

export const mockUseTransactions = {
  transactions: [],
  loading: false,
  error: null,
  addTransaction: mockFn(),
  getTransactionById: mockFn(),
  getTransactionsByDateRange: mockFn(),
};

export const mockUseCart = {
  items: [],
  total: 0,
  itemCount: 0,
  addToCart: mockFn(),
  removeFromCart: mockFn(),
  updateQuantity: mockFn(),
  clearCart: mockFn(),
  applyDiscount: mockFn(),
  removeDiscount: mockFn(),
  discount: null,
};

// Mock data generators
export const createMockService = (overrides = {}) => ({
  id: 'service-1',
  name: 'Test Service',
  description: 'Test service description',
  category: 'printing',
  basePrice: 100,
  isActive: true,
  allowCustomPricing: false,
  requiresNotes: false,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockProduct = (overrides = {}) => ({
  id: 'product-1',
  name: 'Test Product',
  description: 'Test product description',
  category: 'stationery',
  price: 50,
  stockQuantity: 100,
  reorderLevel: 10,
  isActive: true,
  hasExpiry: false,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockTransaction = (overrides = {}) => ({
  id: 'transaction-1',
  items: [
    {
      id: 'item-1',
      type: 'service' as const,
      serviceId: 'service-1',
      name: 'Test Service',
      quantity: 1,
      unitPrice: 100,
      totalPrice: 100,
    },
  ],
  subtotal: 100,
  discount: null,
  total: 100,
  paymentMethod: 'cash' as const,
  customerName: 'Test Customer',
  attendantId: 'test-user-id',
  attendantName: 'Test User',
  createdAt: new Date(),
  ...overrides,
});

// Test data cleanup utilities
export const clearMockCalls = () => {
  // Mock function for development - no-op
};

// Firebase emulator utilities for integration tests
export const setupFirebaseEmulators = () => {
  // This would be used for integration tests with Firebase emulators
  // Implementation depends on specific testing needs
};

export const teardownFirebaseEmulators = () => {
  // Cleanup after integration tests
};
