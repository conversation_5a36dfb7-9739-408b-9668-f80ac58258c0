{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { collection, doc, getDoc, addDoc, updateDoc, deleteDoc, query, orderBy, onSnapshot, serverTimestamp } from 'firebase/firestore';\nimport { db } from '../config/firebase';\nexport const useProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Real-time listener for products\n  useEffect(() => {\n    const productsQuery = query(collection(db, 'products'), orderBy('category'), orderBy('name'));\n    const unsubscribe = onSnapshot(productsQuery, snapshot => {\n      const productsData = [];\n      snapshot.forEach(doc => {\n        var _data$expiryDate, _data$createdAt, _data$updatedAt;\n        const data = doc.data();\n        productsData.push({\n          id: doc.id,\n          name: data.name || '',\n          description: data.description || '',\n          price: data.price || 0,\n          category: data.category || '',\n          stockQuantity: data.stockQuantity || 0,\n          reorderLevel: data.reorderLevel || 0,\n          hasExpiry: data.hasExpiry || false,\n          expiryDate: (_data$expiryDate = data.expiryDate) === null || _data$expiryDate === void 0 ? void 0 : _data$expiryDate.toDate(),\n          isActive: data.isActive !== false,\n          createdAt: ((_data$createdAt = data.createdAt) === null || _data$createdAt === void 0 ? void 0 : _data$createdAt.toDate()) || new Date(),\n          updatedAt: ((_data$updatedAt = data.updatedAt) === null || _data$updatedAt === void 0 ? void 0 : _data$updatedAt.toDate()) || new Date()\n        });\n      });\n      setProducts(productsData);\n      setLoading(false);\n      setError(null);\n    }, error => {\n      console.error('Error fetching products:', error);\n      setError('Failed to fetch products');\n      setLoading(false);\n    });\n    return () => unsubscribe();\n  }, []);\n  const createProduct = async productData => {\n    try {\n      setError(null);\n      await addDoc(collection(db, 'products'), {\n        ...productData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n    } catch (error) {\n      console.error('Error creating product:', error);\n      setError('Failed to create product');\n      throw error;\n    }\n  };\n  const updateProduct = async (productId, updates) => {\n    try {\n      setError(null);\n      const {\n        id,\n        createdAt,\n        ...updateData\n      } = updates;\n      await updateDoc(doc(db, 'products', productId), {\n        ...updateData,\n        updatedAt: serverTimestamp()\n      });\n    } catch (error) {\n      console.error('Error updating product:', error);\n      setError('Failed to update product');\n      throw error;\n    }\n  };\n  const deleteProduct = async productId => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'products', productId));\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      setError('Failed to delete product');\n      throw error;\n    }\n  };\n  const updateStock = async (productId, newQuantity) => {\n    try {\n      setError(null);\n      await updateDoc(doc(db, 'products', productId), {\n        stockQuantity: newQuantity,\n        updatedAt: serverTimestamp()\n      });\n    } catch (error) {\n      console.error('Error updating stock:', error);\n      setError('Failed to update stock');\n      throw error;\n    }\n  };\n  const getProductById = async productId => {\n    try {\n      const productDoc = await getDoc(doc(db, 'products', productId));\n      if (productDoc.exists()) {\n        var _data$expiryDate2, _data$createdAt2, _data$updatedAt2;\n        const data = productDoc.data();\n        return {\n          id: productDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          price: data.price || 0,\n          category: data.category || '',\n          stockQuantity: data.stockQuantity || 0,\n          reorderLevel: data.reorderLevel || 0,\n          hasExpiry: data.hasExpiry || false,\n          expiryDate: (_data$expiryDate2 = data.expiryDate) === null || _data$expiryDate2 === void 0 ? void 0 : _data$expiryDate2.toDate(),\n          isActive: data.isActive !== false,\n          createdAt: ((_data$createdAt2 = data.createdAt) === null || _data$createdAt2 === void 0 ? void 0 : _data$createdAt2.toDate()) || new Date(),\n          updatedAt: ((_data$updatedAt2 = data.updatedAt) === null || _data$updatedAt2 === void 0 ? void 0 : _data$updatedAt2.toDate()) || new Date()\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      throw error;\n    }\n  };\n  const getProductsByCategory = category => {\n    return products.filter(product => product.category === category && product.isActive);\n  };\n  const getActiveProducts = () => {\n    return products.filter(product => product.isActive);\n  };\n  const getInStockProducts = () => {\n    return products.filter(product => product.isActive && product.stockQuantity > 0);\n  };\n  const getLowStockProducts = () => {\n    return products.filter(product => product.isActive && product.stockQuantity <= product.reorderLevel);\n  };\n  const getExpiringProducts = (daysAhead = 30) => {\n    const futureDate = new Date();\n    futureDate.setDate(futureDate.getDate() + daysAhead);\n    return products.filter(product => product.isActive && product.hasExpiry && product.expiryDate && product.expiryDate <= futureDate);\n  };\n  const getProductCategories = () => {\n    const categories = [...new Set(products.map(product => product.category))];\n    return categories.sort();\n  };\n  const searchProducts = searchTerm => {\n    if (!searchTerm.trim()) return getActiveProducts();\n    const term = searchTerm.toLowerCase();\n    return products.filter(product => product.isActive && (product.name.toLowerCase().includes(term) || product.description.toLowerCase().includes(term) || product.category.toLowerCase().includes(term)));\n  };\n  return {\n    products,\n    loading,\n    error,\n    createProduct,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getProductById,\n    getProductsByCategory,\n    getActiveProducts,\n    getInStockProducts,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories,\n    searchProducts\n  };\n};\n_s(useProducts, \"3+N/VFIgZOBgubN9oS5aTzm2qqY=\");", "map": {"version": 3, "names": ["useState", "useEffect", "collection", "doc", "getDoc", "addDoc", "updateDoc", "deleteDoc", "query", "orderBy", "onSnapshot", "serverTimestamp", "db", "useProducts", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "productsQuery", "unsubscribe", "snapshot", "productsData", "for<PERSON>ach", "_data$expiryDate", "_data$createdAt", "_data$updatedAt", "data", "push", "id", "name", "description", "price", "category", "stockQuantity", "reorderLevel", "hasEx<PERSON>ry", "expiryDate", "toDate", "isActive", "createdAt", "Date", "updatedAt", "console", "createProduct", "productData", "updateProduct", "productId", "updates", "updateData", "deleteProduct", "updateStock", "newQuantity", "getProductById", "productDoc", "exists", "_data$expiryDate2", "_data$createdAt2", "_data$updatedAt2", "getProductsByCategory", "filter", "product", "getActiveProducts", "getInStockProducts", "getLowStockProducts", "getExpiringProducts", "daysAhead", "futureDate", "setDate", "getDate", "getProductCategories", "categories", "Set", "map", "sort", "searchProducts", "searchTerm", "trim", "term", "toLowerCase", "includes"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useProducts.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  onSnapshot,\n  serverTimestamp\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { Product } from '../types';\n\nexport const useProducts = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Real-time listener for products\n  useEffect(() => {\n    const productsQuery = query(\n      collection(db, 'products'),\n      orderBy('category'),\n      orderBy('name')\n    );\n\n    const unsubscribe = onSnapshot(\n      productsQuery,\n      (snapshot) => {\n        const productsData: Product[] = [];\n        snapshot.forEach((doc) => {\n          const data = doc.data();\n          productsData.push({\n            id: doc.id,\n            name: data.name || '',\n            description: data.description || '',\n            price: data.price || 0,\n            category: data.category || '',\n            stockQuantity: data.stockQuantity || 0,\n            reorderLevel: data.reorderLevel || 0,\n            hasExpiry: data.hasExpiry || false,\n            expiryDate: data.expiryDate?.toDate(),\n            isActive: data.isActive !== false,\n            createdAt: data.createdAt?.toDate() || new Date(),\n            updatedAt: data.updatedAt?.toDate() || new Date(),\n          });\n        });\n        setProducts(productsData);\n        setLoading(false);\n        setError(null);\n      },\n      (error) => {\n        console.error('Error fetching products:', error);\n        setError('Failed to fetch products');\n        setLoading(false);\n      }\n    );\n\n    return () => unsubscribe();\n  }, []);\n\n  const createProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    try {\n      setError(null);\n      await addDoc(collection(db, 'products'), {\n        ...productData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error creating product:', error);\n      setError('Failed to create product');\n      throw error;\n    }\n  };\n\n  const updateProduct = async (productId: string, updates: Partial<Product>) => {\n    try {\n      setError(null);\n      const { id, createdAt, ...updateData } = updates;\n      await updateDoc(doc(db, 'products', productId), {\n        ...updateData,\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error updating product:', error);\n      setError('Failed to update product');\n      throw error;\n    }\n  };\n\n  const deleteProduct = async (productId: string) => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'products', productId));\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      setError('Failed to delete product');\n      throw error;\n    }\n  };\n\n  const updateStock = async (productId: string, newQuantity: number) => {\n    try {\n      setError(null);\n      await updateDoc(doc(db, 'products', productId), {\n        stockQuantity: newQuantity,\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error updating stock:', error);\n      setError('Failed to update stock');\n      throw error;\n    }\n  };\n\n  const getProductById = async (productId: string): Promise<Product | null> => {\n    try {\n      const productDoc = await getDoc(doc(db, 'products', productId));\n      if (productDoc.exists()) {\n        const data = productDoc.data();\n        return {\n          id: productDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          price: data.price || 0,\n          category: data.category || '',\n          stockQuantity: data.stockQuantity || 0,\n          reorderLevel: data.reorderLevel || 0,\n          hasExpiry: data.hasExpiry || false,\n          expiryDate: data.expiryDate?.toDate(),\n          isActive: data.isActive !== false,\n          createdAt: data.createdAt?.toDate() || new Date(),\n          updatedAt: data.updatedAt?.toDate() || new Date(),\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      throw error;\n    }\n  };\n\n  const getProductsByCategory = (category: string) => {\n    return products.filter(product => product.category === category && product.isActive);\n  };\n\n  const getActiveProducts = () => {\n    return products.filter(product => product.isActive);\n  };\n\n  const getInStockProducts = () => {\n    return products.filter(product => product.isActive && product.stockQuantity > 0);\n  };\n\n  const getLowStockProducts = () => {\n    return products.filter(product => \n      product.isActive && product.stockQuantity <= product.reorderLevel\n    );\n  };\n\n  const getExpiringProducts = (daysAhead: number = 30) => {\n    const futureDate = new Date();\n    futureDate.setDate(futureDate.getDate() + daysAhead);\n    \n    return products.filter(product => \n      product.isActive && \n      product.hasExpiry && \n      product.expiryDate && \n      product.expiryDate <= futureDate\n    );\n  };\n\n  const getProductCategories = () => {\n    const categories = [...new Set(products.map(product => product.category))];\n    return categories.sort();\n  };\n\n  const searchProducts = (searchTerm: string) => {\n    if (!searchTerm.trim()) return getActiveProducts();\n    \n    const term = searchTerm.toLowerCase();\n    return products.filter(product => \n      product.isActive && (\n        product.name.toLowerCase().includes(term) ||\n        product.description.toLowerCase().includes(term) ||\n        product.category.toLowerCase().includes(term)\n      )\n    );\n  };\n\n  return {\n    products,\n    loading,\n    error,\n    createProduct,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getProductById,\n    getProductsByCategory,\n    getActiveProducts,\n    getInStockProducts,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories,\n    searchProducts,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,UAAU,EACVC,GAAG,EAEHC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,KAAK,EAELC,OAAO,EACPC,UAAU,EACVC,eAAe,QACV,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AAGvC,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoB,aAAa,GAAGb,KAAK,CACzBN,UAAU,CAACU,EAAE,EAAE,UAAU,CAAC,EAC1BH,OAAO,CAAC,UAAU,CAAC,EACnBA,OAAO,CAAC,MAAM,CAChB,CAAC;IAED,MAAMa,WAAW,GAAGZ,UAAU,CAC5BW,aAAa,EACZE,QAAQ,IAAK;MACZ,MAAMC,YAAuB,GAAG,EAAE;MAClCD,QAAQ,CAACE,OAAO,CAAEtB,GAAG,IAAK;QAAA,IAAAuB,gBAAA,EAAAC,eAAA,EAAAC,eAAA;QACxB,MAAMC,IAAI,GAAG1B,GAAG,CAAC0B,IAAI,CAAC,CAAC;QACvBL,YAAY,CAACM,IAAI,CAAC;UAChBC,EAAE,EAAE5B,GAAG,CAAC4B,EAAE;UACVC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAE;UACrBC,WAAW,EAAEJ,IAAI,CAACI,WAAW,IAAI,EAAE;UACnCC,KAAK,EAAEL,IAAI,CAACK,KAAK,IAAI,CAAC;UACtBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;UAC7BC,aAAa,EAAEP,IAAI,CAACO,aAAa,IAAI,CAAC;UACtCC,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,CAAC;UACpCC,SAAS,EAAET,IAAI,CAACS,SAAS,IAAI,KAAK;UAClCC,UAAU,GAAAb,gBAAA,GAAEG,IAAI,CAACU,UAAU,cAAAb,gBAAA,uBAAfA,gBAAA,CAAiBc,MAAM,CAAC,CAAC;UACrCC,QAAQ,EAAEZ,IAAI,CAACY,QAAQ,KAAK,KAAK;UACjCC,SAAS,EAAE,EAAAf,eAAA,GAAAE,IAAI,CAACa,SAAS,cAAAf,eAAA,uBAAdA,eAAA,CAAgBa,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC,CAAC;UACjDC,SAAS,EAAE,EAAAhB,eAAA,GAAAC,IAAI,CAACe,SAAS,cAAAhB,eAAA,uBAAdA,eAAA,CAAgBY,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;MACF3B,WAAW,CAACQ,YAAY,CAAC;MACzBN,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,EACAD,KAAK,IAAK;MACT0B,OAAO,CAAC1B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0BAA0B,CAAC;MACpCF,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMI,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwB,aAAa,GAAG,MAAOC,WAA4D,IAAK;IAC5F,IAAI;MACF3B,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMf,MAAM,CAACH,UAAU,CAACU,EAAE,EAAE,UAAU,CAAC,EAAE;QACvC,GAAGmC,WAAW;QACdL,SAAS,EAAE/B,eAAe,CAAC,CAAC;QAC5BiC,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAM6B,aAAa,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,OAAyB,KAAK;IAC5E,IAAI;MACF9B,QAAQ,CAAC,IAAI,CAAC;MACd,MAAM;QAAEW,EAAE;QAAEW,SAAS;QAAE,GAAGS;MAAW,CAAC,GAAGD,OAAO;MAChD,MAAM5C,SAAS,CAACH,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEqC,SAAS,CAAC,EAAE;QAC9C,GAAGE,UAAU;QACbP,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMiC,aAAa,GAAG,MAAOH,SAAiB,IAAK;IACjD,IAAI;MACF7B,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMb,SAAS,CAACJ,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEqC,SAAS,CAAC,CAAC;IACjD,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMkC,WAAW,GAAG,MAAAA,CAAOJ,SAAiB,EAAEK,WAAmB,KAAK;IACpE,IAAI;MACFlC,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMd,SAAS,CAACH,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEqC,SAAS,CAAC,EAAE;QAC9Cb,aAAa,EAAEkB,WAAW;QAC1BV,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,wBAAwB,CAAC;MAClC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMoC,cAAc,GAAG,MAAON,SAAiB,IAA8B;IAC3E,IAAI;MACF,MAAMO,UAAU,GAAG,MAAMpD,MAAM,CAACD,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEqC,SAAS,CAAC,CAAC;MAC/D,IAAIO,UAAU,CAACC,MAAM,CAAC,CAAC,EAAE;QAAA,IAAAC,iBAAA,EAAAC,gBAAA,EAAAC,gBAAA;QACvB,MAAM/B,IAAI,GAAG2B,UAAU,CAAC3B,IAAI,CAAC,CAAC;QAC9B,OAAO;UACLE,EAAE,EAAEyB,UAAU,CAACzB,EAAE;UACjBC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAE;UACrBC,WAAW,EAAEJ,IAAI,CAACI,WAAW,IAAI,EAAE;UACnCC,KAAK,EAAEL,IAAI,CAACK,KAAK,IAAI,CAAC;UACtBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;UAC7BC,aAAa,EAAEP,IAAI,CAACO,aAAa,IAAI,CAAC;UACtCC,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,CAAC;UACpCC,SAAS,EAAET,IAAI,CAACS,SAAS,IAAI,KAAK;UAClCC,UAAU,GAAAmB,iBAAA,GAAE7B,IAAI,CAACU,UAAU,cAAAmB,iBAAA,uBAAfA,iBAAA,CAAiBlB,MAAM,CAAC,CAAC;UACrCC,QAAQ,EAAEZ,IAAI,CAACY,QAAQ,KAAK,KAAK;UACjCC,SAAS,EAAE,EAAAiB,gBAAA,GAAA9B,IAAI,CAACa,SAAS,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBnB,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC,CAAC;UACjDC,SAAS,EAAE,EAAAgB,gBAAA,GAAA/B,IAAI,CAACe,SAAS,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBpB,MAAM,CAAC,CAAC,KAAI,IAAIG,IAAI,CAAC;QAClD,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM0C,qBAAqB,GAAI1B,QAAgB,IAAK;IAClD,OAAOpB,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC5B,QAAQ,KAAKA,QAAQ,IAAI4B,OAAO,CAACtB,QAAQ,CAAC;EACtF,CAAC;EAED,MAAMuB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOjD,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACtB,QAAQ,CAAC;EACrD,CAAC;EAED,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAOlD,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACtB,QAAQ,IAAIsB,OAAO,CAAC3B,aAAa,GAAG,CAAC,CAAC;EAClF,CAAC;EAED,MAAM8B,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOnD,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAC5BA,OAAO,CAACtB,QAAQ,IAAIsB,OAAO,CAAC3B,aAAa,IAAI2B,OAAO,CAAC1B,YACvD,CAAC;EACH,CAAC;EAED,MAAM8B,mBAAmB,GAAGA,CAACC,SAAiB,GAAG,EAAE,KAAK;IACtD,MAAMC,UAAU,GAAG,IAAI1B,IAAI,CAAC,CAAC;IAC7B0B,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAGH,SAAS,CAAC;IAEpD,OAAOrD,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAC5BA,OAAO,CAACtB,QAAQ,IAChBsB,OAAO,CAACzB,SAAS,IACjByB,OAAO,CAACxB,UAAU,IAClBwB,OAAO,CAACxB,UAAU,IAAI8B,UACxB,CAAC;EACH,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3D,QAAQ,CAAC4D,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAAC5B,QAAQ,CAAC,CAAC,CAAC;IAC1E,OAAOsC,UAAU,CAACG,IAAI,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,cAAc,GAAIC,UAAkB,IAAK;IAC7C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE,OAAOf,iBAAiB,CAAC,CAAC;IAElD,MAAMgB,IAAI,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;IACrC,OAAOlE,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAC5BA,OAAO,CAACtB,QAAQ,KACdsB,OAAO,CAAC/B,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,IACzCjB,OAAO,CAAC9B,WAAW,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,IAChDjB,OAAO,CAAC5B,QAAQ,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,CAEjD,CAAC;EACH,CAAC;EAED,OAAO;IACLjE,QAAQ;IACRE,OAAO;IACPE,KAAK;IACL2B,aAAa;IACbE,aAAa;IACbI,aAAa;IACbC,WAAW;IACXE,cAAc;IACdM,qBAAqB;IACrBG,iBAAiB;IACjBC,kBAAkB;IAClBC,mBAAmB;IACnBC,mBAAmB;IACnBK,oBAAoB;IACpBK;EACF,CAAC;AACH,CAAC;AAAC/D,EAAA,CAnMWD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}