{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 17V9\",\n  key: \"1fwyjl\"\n}], [\"path\", {\n  d: \"M18 17v-3\",\n  key: \"1sqioe\"\n}], [\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"M8 17V5\",\n  key: \"1wzmnc\"\n}]];\nconst ChartColumnDecreasing = createLucideIcon(\"chart-column-decreasing\", __iconNode);\nexport { __iconNode, ChartColumnDecreasing as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ChartColumnDecreasing", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\chart-column-decreasing.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13 17V9', key: '1fwyjl' }],\n  ['path', { d: 'M18 17v-3', key: '1sqioe' }],\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['path', { d: 'M8 17V5', key: '1wzmnc' }],\n];\n\n/**\n * @component @name ChartColumnDecreasing\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgMTdWOSIgLz4KICA8cGF0aCBkPSJNMTggMTd2LTMiIC8+CiAgPHBhdGggZD0iTTMgM3YxNmEyIDIgMCAwIDAgMiAyaDE2IiAvPgogIDxwYXRoIGQ9Ik04IDE3VjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column-decreasing\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartColumnDecreasing = createLucideIcon('chart-column-decreasing', __iconNode);\n\nexport default ChartColumnDecreasing;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,qBAAA,GAAwBC,gBAAiB,4BAA2BJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}