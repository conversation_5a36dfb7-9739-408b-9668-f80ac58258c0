{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A container for all of the Logger instances\n */\nconst instances = [];\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n  LogLevel[LogLevel[\"VERBOSE\"] = 1] = \"VERBOSE\";\n  LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n  LogLevel[LogLevel[\"WARN\"] = 3] = \"WARN\";\n  LogLevel[LogLevel[\"ERROR\"] = 4] = \"ERROR\";\n  LogLevel[LogLevel[\"SILENT\"] = 5] = \"SILENT\";\n})(LogLevel || (LogLevel = {}));\nconst levelStringToEnum = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n/**\n * The default log level\n */\nconst defaultLogLevel = LogLevel.INFO;\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler = (instance, logType, ...args) => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType];\n  if (method) {\n    console[method](`[${now}]  ${instance.name}:`, ...args);\n  } else {\n    throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);\n  }\n};\nclass Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(name) {\n    this.name = name;\n    /**\n     * The log level of the given Logger instance.\n     */\n    this._logLevel = defaultLogLevel;\n    /**\n     * The main (internal) log handler for the Logger instance.\n     * Can be set to a new function in internal package code but not by user.\n     */\n    this._logHandler = defaultLogHandler;\n    /**\n     * The optional, additional, user-defined log handler for the Logger instance.\n     */\n    this._userLogHandler = null;\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n  get logLevel() {\n    return this._logLevel;\n  }\n  set logLevel(val) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val) {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n  get logHandler() {\n    return this._logHandler;\n  }\n  set logHandler(val) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n  get userLogHandler() {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val) {\n    this._userLogHandler = val;\n  }\n  /**\n   * The functions below are all based on the `console` interface\n   */\n  debug(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\nfunction setLogLevel(level) {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\nfunction setUserLogHandler(logCallback, options) {\n  for (const instance of instances) {\n    let customLogLevel = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (instance, level, ...args) => {\n        const message = args.map(arg => {\n          if (arg == null) {\n            return null;\n          } else if (typeof arg === 'string') {\n            return arg;\n          } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n            return arg.toString();\n          } else if (arg instanceof Error) {\n            return arg.message;\n          } else {\n            try {\n              return JSON.stringify(arg);\n            } catch (ignored) {\n              return null;\n            }\n          }\n        }).filter(arg => arg).join(' ');\n        if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase(),\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\nexport { LogLevel, Logger, setLogLevel, setUserLogHandler };", "map": {"version": 3, "names": ["instances", "LogLevel", "levelStringToEnum", "DEBUG", "VERBOSE", "INFO", "WARN", "ERROR", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "name", "Error", "<PERSON><PERSON>", "constructor", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "push", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "debug", "log", "info", "warn", "error", "level", "for<PERSON>ach", "inst", "setUserLogHandler", "logCallback", "options", "customLogLevel", "message", "map", "arg", "toString", "JSON", "stringify", "ignored", "filter", "join", "toLowerCase", "type"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\@firebase\\logger\\src\\logger.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;AAeG;AAuBH;;AAEG;AACI,MAAMA,SAAS,GAAa,EAAE;AAErC;;;;;;;;;;AAUG;IACSC,QAAA;AAAZ,WAAYA,QAAQ;EAClBA,QAAA,CAAAA,QAAA,wBAAK;EACLA,QAAA,CAAAA,QAAA,4BAAO;EACPA,QAAA,CAAAA,QAAA,sBAAI;EACJA,QAAA,CAAAA,QAAA,sBAAI;EACJA,QAAA,CAAAA,QAAA,wBAAK;EACLA,QAAA,CAAAA,QAAA,0BAAM;AACR,CAAC,EAPWA,QAAQ,KAARA,QAAQ,GAOnB;AAED,MAAMC,iBAAiB,GAA0C;EAC/D,OAAO,EAAED,QAAQ,CAACE,KAAK;EACvB,SAAS,EAAEF,QAAQ,CAACG,OAAO;EAC3B,MAAM,EAAEH,QAAQ,CAACI,IAAI;EACrB,MAAM,EAAEJ,QAAQ,CAACK,IAAI;EACrB,OAAO,EAAEL,QAAQ,CAACM,KAAK;EACvB,QAAQ,EAAEN,QAAQ,CAACO;CACpB;AAED;;AAEG;AACH,MAAMC,eAAe,GAAaR,QAAQ,CAACI,IAAI;AAa/C;;;;;AAKG;AACH,MAAMK,aAAa,GAAG;EACpB,CAACT,QAAQ,CAACE,KAAK,GAAG,KAAK;EACvB,CAACF,QAAQ,CAACG,OAAO,GAAG,KAAK;EACzB,CAACH,QAAQ,CAACI,IAAI,GAAG,MAAM;EACvB,CAACJ,QAAQ,CAACK,IAAI,GAAG,MAAM;EACvB,CAACL,QAAQ,CAACM,KAAK,GAAG;CACnB;AAED;;;;AAIG;AACH,MAAMI,iBAAiB,GAAeA,CAACC,QAAQ,EAAEC,OAAO,EAAE,GAAGC,IAAI,KAAU;EACzE,IAAID,OAAO,GAAGD,QAAQ,CAACG,QAAQ,EAAE;IAC/B;;EAEF,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACpC,MAAMC,MAAM,GAAGT,aAAa,CAACG,OAAqC,CAAC;EACnE,IAAIM,MAAM,EAAE;IACVC,OAAO,CAACD,MAA2C,CAAC,CAClD,IAAIH,GAAG,MAAMJ,QAAQ,CAACS,IAAI,GAAG,EAC7B,GAAGP,IAAI,CACR;GACF,MAAM;IACL,MAAM,IAAIQ,KAAK,CACb,8DAA8DT,OAAO,GAAG,CACzE;;AAEL,CAAC;MAEYU,MAAM;EACjB;;;;;AAKG;EACHC,YAAmBH,IAAY;IAAZ,IAAI,CAAAA,IAAA,GAAJA,IAAI;IAOvB;;AAEG;IACK,IAAS,CAAAI,SAAA,GAAGhB,eAAe;IAkBnC;;;AAGG;IACK,IAAW,CAAAiB,WAAA,GAAef,iBAAiB;IAWnD;;AAEG;IACK,IAAe,CAAAgB,eAAA,GAAsB,IAAI;IA7C/C;;AAEG;IACH3B,SAAS,CAAC4B,IAAI,CAAC,IAAI,CAAC;;EAQtB,IAAIb,QAAQA,CAAA;IACV,OAAO,IAAI,CAACU,SAAS;;EAGvB,IAAIV,QAAQA,CAACc,GAAa;IACxB,IAAI,EAAEA,GAAG,IAAI5B,QAAQ,CAAC,EAAE;MACtB,MAAM,IAAI6B,SAAS,CAAC,kBAAkBD,GAAG,4BAA4B,CAAC;;IAExE,IAAI,CAACJ,SAAS,GAAGI,GAAG;;;EAItBE,WAAWA,CAACF,GAA8B;IACxC,IAAI,CAACJ,SAAS,GAAG,OAAOI,GAAG,KAAK,QAAQ,GAAG3B,iBAAiB,CAAC2B,GAAG,CAAC,GAAGA,GAAG;;EAQzE,IAAIG,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACN,WAAW;;EAEzB,IAAIM,UAAUA,CAACH,GAAe;IAC5B,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;MAC7B,MAAM,IAAIC,SAAS,CAAC,mDAAmD,CAAC;;IAE1E,IAAI,CAACJ,WAAW,GAAGG,GAAG;;EAOxB,IAAII,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACN,eAAe;;EAE7B,IAAIM,cAAcA,CAACJ,GAAsB;IACvC,IAAI,CAACF,eAAe,GAAGE,GAAG;;EAG5B;;AAEG;EAEHK,KAAKA,CAAC,GAAGpB,IAAe;IACtB,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACE,KAAK,EAAE,GAAGW,IAAI,CAAC;IAC3E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACE,KAAK,EAAE,GAAGW,IAAI,CAAC;;EAEjDqB,GAAGA,CAAC,GAAGrB,IAAe;IACpB,IAAI,CAACa,eAAe,IAClB,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACG,OAAO,EAAE,GAAGU,IAAI,CAAC;IACvD,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACG,OAAO,EAAE,GAAGU,IAAI,CAAC;;EAEnDsB,IAAIA,CAAC,GAAGtB,IAAe;IACrB,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACI,IAAI,EAAE,GAAGS,IAAI,CAAC;IAC1E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACI,IAAI,EAAE,GAAGS,IAAI,CAAC;;EAEhDuB,IAAIA,CAAC,GAAGvB,IAAe;IACrB,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACK,IAAI,EAAE,GAAGQ,IAAI,CAAC;IAC1E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACK,IAAI,EAAE,GAAGQ,IAAI,CAAC;;EAEhDwB,KAAKA,CAAC,GAAGxB,IAAe;IACtB,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACM,KAAK,EAAE,GAAGO,IAAI,CAAC;IAC3E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACM,KAAK,EAAE,GAAGO,IAAI,CAAC;;AAElD;AAEK,SAAUiB,WAAWA,CAACQ,KAAgC;EAC1DvC,SAAS,CAACwC,OAAO,CAACC,IAAI,IAAG;IACvBA,IAAI,CAACV,WAAW,CAACQ,KAAK,CAAC;EACzB,CAAC,CAAC;AACJ;AAEgB,SAAAG,iBAAiBA,CAC/BC,WAA+B,EAC/BC,OAAoB;EAEpB,KAAK,MAAMhC,QAAQ,IAAIZ,SAAS,EAAE;IAChC,IAAI6C,cAAc,GAAoB,IAAI;IAC1C,IAAID,OAAO,IAAIA,OAAO,CAACL,KAAK,EAAE;MAC5BM,cAAc,GAAG3C,iBAAiB,CAAC0C,OAAO,CAACL,KAAK,CAAC;;IAEnD,IAAII,WAAW,KAAK,IAAI,EAAE;MACxB/B,QAAQ,CAACqB,cAAc,GAAG,IAAI;KAC/B,MAAM;MACLrB,QAAQ,CAACqB,cAAc,GAAG,CACxBrB,QAAgB,EAChB2B,KAAe,EACf,GAAGzB,IAAe,KAChB;QACF,MAAMgC,OAAO,GAAGhC,IAAI,CACjBiC,GAAG,CAACC,GAAG,IAAG;UACT,IAAIA,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,IAAI;WACZ,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YAClC,OAAOA,GAAG;WACX,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;YAC9D,OAAOA,GAAG,CAACC,QAAQ,EAAE;WACtB,MAAM,IAAID,GAAG,YAAY1B,KAAK,EAAE;YAC/B,OAAO0B,GAAG,CAACF,OAAO;WACnB,MAAM;YACL,IAAI;cACF,OAAOI,IAAI,CAACC,SAAS,CAACH,GAAG,CAAC;aAC3B,CAAC,OAAOI,OAAO,EAAE;cAChB,OAAO,IAAI;;;QAGjB,CAAC,CAAC,CACDC,MAAM,CAACL,GAAG,IAAIA,GAAG,CAAC,CAClBM,IAAI,CAAC,GAAG,CAAC;QACZ,IAAIf,KAAK,KAAKM,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAIjC,QAAQ,CAACG,QAAQ,CAAC,EAAE;UAClD4B,WAAW,CAAC;YACVJ,KAAK,EAAEtC,QAAQ,CAACsC,KAAK,CAAC,CAACgB,WAAW,EAAoB;YACtDT,OAAO;YACPhC,IAAI;YACJ0C,IAAI,EAAE5C,QAAQ,CAACS;UAChB,EAAC;;MAEN,CAAC;;;AAGP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}