{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\",\n  key: \"yt0hxn\"\n}], [\"polyline\", {\n  points: \"7.5 4.21 12 6.81 16.5 4.21\",\n  key: \"fabo96\"\n}], [\"polyline\", {\n  points: \"7.5 19.79 7.5 14.6 3 12\",\n  key: \"z377f1\"\n}], [\"polyline\", {\n  points: \"21 12 16.5 14.6 16.5 19.79\",\n  key: \"9nrev1\"\n}], [\"polyline\", {\n  points: \"3.27 6.96 12 12.01 20.73 6.96\",\n  key: \"1180pa\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22.08\",\n  y2: \"12\",\n  key: \"3z3uq6\"\n}]];\nconst Codesandbox = createLucideIcon(\"codesandbox\", __iconNode);\nexport { __iconNode, Codesandbox as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "points", "x1", "x2", "y1", "y2", "Codesandbox", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\codesandbox.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z',\n      key: 'yt0hxn',\n    },\n  ],\n  ['polyline', { points: '7.5 4.21 12 6.81 16.5 4.21', key: 'fabo96' }],\n  ['polyline', { points: '7.5 19.79 7.5 14.6 3 12', key: 'z377f1' }],\n  ['polyline', { points: '21 12 16.5 14.6 16.5 19.79', key: '9nrev1' }],\n  ['polyline', { points: '3.27 6.96 12 12.01 20.73 6.96', key: '1180pa' }],\n  ['line', { x1: '12', x2: '12', y1: '22.08', y2: '12', key: '3z3uq6' }],\n];\n\n/**\n * @component @name Codesandbox\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTZWOGEyIDIgMCAwIDAtMS0xLjczbC03LTRhMiAyIDAgMCAwLTIgMGwtNyA0QTIgMiAwIDAgMCAzIDh2OGEyIDIgMCAwIDAgMSAxLjczbDcgNGEyIDIgMCAwIDAgMiAwbDctNEEyIDIgMCAwIDAgMjEgMTZ6IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjcuNSA0LjIxIDEyIDYuODEgMTYuNSA0LjIxIiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjcuNSAxOS43OSA3LjUgMTQuNiAzIDEyIiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjIxIDEyIDE2LjUgMTQuNiAxNi41IDE5Ljc5IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjMuMjcgNi45NiAxMiAxMi4wMSAyMC43MyA2Ljk2IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMjIuMDgiIHkyPSIxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/codesandbox\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n * @deprecated Brand icons have been deprecated and are due to be removed, please refer to https://github.com/lucide-icons/lucide/issues/670. We recommend using https://simpleicons.org/?q=codesandbox instead. This icon will be removed in v1.0\n */\nconst Codesandbox = createLucideIcon('codesandbox', __iconNode);\n\nexport default Codesandbox;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,4BAA8B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAEC,MAAA,EAAQ,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,UAAY;EAAEC,MAAA,EAAQ,4BAA8B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAEC,MAAA,EAAQ,+BAAiC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,QAAQ;EAAEE,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAL,GAAA,EAAK;AAAU,GACvE;AAaM,MAAAM,WAAA,GAAcC,gBAAiB,gBAAeT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}