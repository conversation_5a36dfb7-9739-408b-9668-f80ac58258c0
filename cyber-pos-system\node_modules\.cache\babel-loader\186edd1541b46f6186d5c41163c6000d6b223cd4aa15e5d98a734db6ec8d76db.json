{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.852 14.772-.383.923\",\n  key: \"11vil6\"\n}], [\"path\", {\n  d: \"m10.852 9.228-.383-.923\",\n  key: \"1fjppe\"\n}], [\"path\", {\n  d: \"m13.148 14.772.382.924\",\n  key: \"je3va1\"\n}], [\"path\", {\n  d: \"m13.531 8.305-.383.923\",\n  key: \"18epck\"\n}], [\"path\", {\n  d: \"m14.772 10.852.923-.383\",\n  key: \"k9m8cz\"\n}], [\"path\", {\n  d: \"m14.772 13.148.923.383\",\n  key: \"1xvhww\"\n}], [\"path\", {\n  d: \"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 0 0-5.63-1.446 3 3 0 0 0-.368 1.571 4 4 0 0 0-2.525 5.771\",\n  key: \"jcbbz1\"\n}], [\"path\", {\n  d: \"M17.998 5.125a4 4 0 0 1 2.525 5.771\",\n  key: \"1kkn7e\"\n}], [\"path\", {\n  d: \"M19.505 10.294a4 4 0 0 1-1.5 7.706\",\n  key: \"18bmuc\"\n}], [\"path\", {\n  d: \"M4.032 17.483A4 4 0 0 0 11.464 20c.18-.311.892-.311 1.072 0a4 4 0 0 0 7.432-2.516\",\n  key: \"uozx0d\"\n}], [\"path\", {\n  d: \"M4.5 10.291A4 4 0 0 0 6 18\",\n  key: \"whdemb\"\n}], [\"path\", {\n  d: \"M6.002 5.125a3 3 0 0 0 .4 1.375\",\n  key: \"1kqy2g\"\n}], [\"path\", {\n  d: \"m9.228 10.852-.923-.383\",\n  key: \"1wtb30\"\n}], [\"path\", {\n  d: \"m9.228 13.148-.923.383\",\n  key: \"1a830x\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}]];\nconst BrainCog = createLucideIcon(\"brain-cog\", __iconNode);\nexport { __iconNode, BrainCog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "BrainCog", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\brain-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10.852 14.772-.383.923', key: '11vil6' }],\n  ['path', { d: 'm10.852 9.228-.383-.923', key: '1fjppe' }],\n  ['path', { d: 'm13.148 14.772.382.924', key: 'je3va1' }],\n  ['path', { d: 'm13.531 8.305-.383.923', key: '18epck' }],\n  ['path', { d: 'm14.772 10.852.923-.383', key: 'k9m8cz' }],\n  ['path', { d: 'm14.772 13.148.923.383', key: '1xvhww' }],\n  [\n    'path',\n    {\n      d: 'M17.598 6.5A3 3 0 1 0 12 5a3 3 0 0 0-5.63-1.446 3 3 0 0 0-.368 1.571 4 4 0 0 0-2.525 5.771',\n      key: 'jcbbz1',\n    },\n  ],\n  ['path', { d: 'M17.998 5.125a4 4 0 0 1 2.525 5.771', key: '1kkn7e' }],\n  ['path', { d: 'M19.505 10.294a4 4 0 0 1-1.5 7.706', key: '18bmuc' }],\n  [\n    'path',\n    {\n      d: 'M4.032 17.483A4 4 0 0 0 11.464 20c.18-.311.892-.311 1.072 0a4 4 0 0 0 7.432-2.516',\n      key: 'uozx0d',\n    },\n  ],\n  ['path', { d: 'M4.5 10.291A4 4 0 0 0 6 18', key: 'whdemb' }],\n  ['path', { d: 'M6.002 5.125a3 3 0 0 0 .4 1.375', key: '1kqy2g' }],\n  ['path', { d: 'm9.228 10.852-.923-.383', key: '1wtb30' }],\n  ['path', { d: 'm9.228 13.148-.923.383', key: '1a830x' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name BrainCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAuODUyIDE0Ljc3Mi0uMzgzLjkyMyIgLz4KICA8cGF0aCBkPSJtMTAuODUyIDkuMjI4LS4zODMtLjkyMyIgLz4KICA8cGF0aCBkPSJtMTMuMTQ4IDE0Ljc3Mi4zODIuOTI0IiAvPgogIDxwYXRoIGQ9Im0xMy41MzEgOC4zMDUtLjM4My45MjMiIC8+CiAgPHBhdGggZD0ibTE0Ljc3MiAxMC44NTIuOTIzLS4zODMiIC8+CiAgPHBhdGggZD0ibTE0Ljc3MiAxMy4xNDguOTIzLjM4MyIgLz4KICA8cGF0aCBkPSJNMTcuNTk4IDYuNUEzIDMgMCAxIDAgMTIgNWEzIDMgMCAwIDAtNS42My0xLjQ0NiAzIDMgMCAwIDAtLjM2OCAxLjU3MSA0IDQgMCAwIDAtMi41MjUgNS43NzEiIC8+CiAgPHBhdGggZD0iTTE3Ljk5OCA1LjEyNWE0IDQgMCAwIDEgMi41MjUgNS43NzEiIC8+CiAgPHBhdGggZD0iTTE5LjUwNSAxMC4yOTRhNCA0IDAgMCAxLTEuNSA3LjcwNiIgLz4KICA8cGF0aCBkPSJNNC4wMzIgMTcuNDgzQTQgNCAwIDAgMCAxMS40NjQgMjBjLjE4LS4zMTEuODkyLS4zMTEgMS4wNzIgMGE0IDQgMCAwIDAgNy40MzItMi41MTYiIC8+CiAgPHBhdGggZD0iTTQuNSAxMC4yOTFBNCA0IDAgMCAwIDYgMTgiIC8+CiAgPHBhdGggZD0iTTYuMDAyIDUuMTI1YTMgMyAwIDAgMCAuNCAxLjM3NSIgLz4KICA8cGF0aCBkPSJtOS4yMjggMTAuODUyLS45MjMtLjM4MyIgLz4KICA8cGF0aCBkPSJtOS4yMjggMTMuMTQ4LS45MjMuMzgzIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/brain-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BrainCog = createLucideIcon('brain-cog', __iconNode);\n\nexport default BrainCog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,QAAA,GAAWC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}