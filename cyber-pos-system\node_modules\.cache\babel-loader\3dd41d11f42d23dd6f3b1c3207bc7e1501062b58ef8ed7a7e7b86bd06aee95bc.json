{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 10H6a4 4 0 0 1-4-4 1 1 0 0 1 1-1h4\",\n  key: \"1hjpb6\"\n}], [\"path\", {\n  d: \"M7 5a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1 7 7 0 0 1-7 7H8a1 1 0 0 1-1-1z\",\n  key: \"1qn45f\"\n}], [\"path\", {\n  d: \"M9 12v5\",\n  key: \"3anwtq\"\n}], [\"path\", {\n  d: \"M15 12v5\",\n  key: \"5xh3zn\"\n}], [\"path\", {\n  d: \"M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3 1 1 0 0 1-1 1H6a1 1 0 0 1-1-1\",\n  key: \"1fi4x8\"\n}]];\nconst Anvil = createLucideIcon(\"anvil\", __iconNode);\nexport { __iconNode, Anvil as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "An<PERSON>", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\anvil.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7 10H6a4 4 0 0 1-4-4 1 1 0 0 1 1-1h4', key: '1hjpb6' }],\n  [\n    'path',\n    { d: 'M7 5a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1 7 7 0 0 1-7 7H8a1 1 0 0 1-1-1z', key: '1qn45f' },\n  ],\n  ['path', { d: 'M9 12v5', key: '3anwtq' }],\n  ['path', { d: 'M15 12v5', key: '5xh3zn' }],\n  [\n    'path',\n    { d: 'M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3 1 1 0 0 1-1 1H6a1 1 0 0 1-1-1', key: '1fi4x8' },\n  ],\n];\n\n/**\n * @component @name Anvil\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAxMEg2YTQgNCAwIDAgMS00LTQgMSAxIDAgMCAxIDEtMWg0IiAvPgogIDxwYXRoIGQ9Ik03IDVhMSAxIDAgMCAxIDEtMWgxM2ExIDEgMCAwIDEgMSAxIDcgNyAwIDAgMS03IDdIOGExIDEgMCAwIDEtMS0xeiIgLz4KICA8cGF0aCBkPSJNOSAxMnY1IiAvPgogIDxwYXRoIGQ9Ik0xNSAxMnY1IiAvPgogIDxwYXRoIGQ9Ik01IDIwYTMgMyAwIDAgMSAzLTNoOGEzIDMgMCAwIDEgMyAzIDEgMSAwIDAgMS0xIDFINmExIDEgMCAwIDEtMS0xIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/anvil\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Anvil = createLucideIcon('anvil', __iconNode);\n\nexport default Anvil;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CACE,QACA;EAAED,CAAA,EAAG,oEAAsE;EAAAC,GAAA,EAAK;AAAS,EAC3F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EAAED,CAAA,EAAG,mEAAqE;EAAAC,GAAA,EAAK;AAAS,GAE5F;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}